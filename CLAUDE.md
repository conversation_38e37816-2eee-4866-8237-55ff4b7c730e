# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build Commands
- **Build**: Use `./scripts/build/build.sh` to build the project. Options:
  - `-p, --platform=PLATFORM`: Target platform (native|am335x|zynq|2440|ec20) [default: native]
  - `-t, --type=TYPE`: Build type (Debug|Release) [default: Release]
  - `-j, --jobs=NUM`: Parallel compilation jobs [default: CPU cores]
  - `-c, --clean`: Clean build directory
  - `-r, --rebuild-deps`: Force rebuild third-party libraries
  - `-i, --install`: Install build results
  - `-k, --package`: Create package
  - `-v, --verbose`: Verbose output mode
- **Clean**: `./scripts/build/build.sh --clean`
- **Debug Build**: `./scripts/build/build.sh --type=Debug`
- **Cross-compile**: `./scripts/build/build.sh --platform=am335x`

## Project Overview
This is a **refactored embedded web configuration system** that provides a modernized web interface for configuring various specialized communication devices. The system maintains 100% functional compatibility with the original CGI-based implementation while upgrading to a REST API architecture.

## High-Level Architecture

### Core Components
- **webcfg-server**: Main HTTP server executable (C + libmicrohttpd)
- **webcfg_utils**: Shared utility library for common operations
- **Web Interface**: Modern HTML5/CSS3/JavaScript single-page application
- **API Layer**: RESTful JSON APIs that replace original CGI programs

### Architecture Layers
1. **Frontend**: Single-page web application (replaces frameset layout)
2. **API Service**: REST API server (replaces individual CGI programs)
3. **Business Logic**: Configuration management modules
4. **Data Access**: Binary and INI configuration file handlers

### Configuration Modules
The system is organized into configuration modules that directly correspond to original CGI programs:

| Module | Original CGI | Functionality |
|--------|-------------|---------------|
| `board_config` | Board configuration | Basic device settings |
| `board_ntp` | `0ntp.c` | NTP time synchronization |
| `center_config` | `0center.c` | Call center configuration |
| `gateway_config` | `0gateway.c` | Internet gateway settings |
| `recorder_config` | `0recorder.c/0mini.c` | Recording module settings |
| `sci_config` | `0sci*.c` | Base station controller |
| `switch_config` | `0switch*.c` | Switching module settings |
| `auth_handler` | `0passwd.c` | Password management |
| `system_mgmt` | `0system.c/0down.c` | System management & logs |

## Third-Party Libraries
- **cjson**: JSON parsing and generation
- **microhttpd**: Lightweight HTTP server library  
- **cgic**: CGI compatibility support (legacy)

## Key Directories
- `src/`: C source code organized by functional modules
  - `api/`: API routing and request handling
  - `config/`: Device configuration modules
  - `auth/`: Authentication and session management
  - `system/`: System management operations
  - `utils/`: Shared utility functions
- `web/`: Frontend web application files
- `3rdparty/`: Third-party library source code
- `scripts/build/`: Build automation scripts
- `cmake/`: CMake build configuration and toolchains
- `docs/`: Project documentation and analysis reports

## Development Guidelines

### Strict Constraints
This is a **compatibility-focused refactoring project** with strict constraints:
- **NO new functionality** - only modernize existing features
- **100% configuration file compatibility** - must work with existing binary/INI files
- **Maintain simple complexity** - do not add business logic complexity
- **Direct CGI correspondence** - each API must map to an original CGI program

### Adding New Features
**WARNING**: Do not add new features beyond what existed in the original CGI system. When implementing:
- Check `docs/01_项目分析报告.md` for original CGI functionality scope
- Ensure any new code directly corresponds to existing CGI behavior
- Maintain the simple, configuration-focused nature of the system

### Configuration File Formats
- Binary configuration files in `/home/<USER>/cfg/` (e.g., `board.cfg`, `center.cfg`)
- INI format files in `/etc/` (e.g., `eth0-setting`, `wlan0-setting`)
- **Do not modify file formats or add new configuration options**

### API Development
- All APIs are RESTful JSON under `/api/v1/`
- Each endpoint strictly corresponds to original CGI functionality
- Use standardized request/response formats
- Implement proper error handling and validation

## Testing and Deployment
- Use `./scripts/tests/test.sh` for automated testing
- Deploy with `./scripts/deploy/deploy.sh`
- Service management via systemd
- Multi-platform cross-compilation support

## Important Notes
- This system replaces a **simple CGI-based configuration interface**
- Original system had **no complex business logic** - maintain this simplicity
- Focus on **architectural modernization** rather than feature enhancement
- Refer to project documentation in `docs/` for detailed requirements and constraints