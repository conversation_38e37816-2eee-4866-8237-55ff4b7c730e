#! /bin/sh
cp update_all update
echo "build cgis ..."

#cd /home/<USER>/work/cgic205
#./build
#cd /home/<USER>/work/webcfg
cd /opt/share/cloud/syncall/SyncWork/linux/shell/webcfg
./build.sh

cd /opt/share/cloud/syncall/SyncWork/linux/shell/webcfg

tar czf ipweb.tar.gz ipweb
tar czf ipweb.bin ipweb.tar.gz update

tar czf ipweb2.tar.gz ipweb2
tar czf ipweb2.bin ipweb2.tar.gz update

mv *.bin /home/<USER>/klive/release/sysupdate/
rm *.tar.gz
rm update
