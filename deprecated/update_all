#! /bin/sh

if [ -f ipweb.tar.gz ] ; then
/bin/tar xzf ipweb.tar.gz
/bin/rm -r /www
/bin/mv ipweb /www
/bin/rm ipweb.tar.gz
fi

if [ -f ipweb2.tar.gz ] ; then
/bin/tar xzf ipweb2.tar.gz
/bin/rm -r /www
/bin/mv ipweb2 /www
/bin/rm ipweb2.tar.gz
fi

if [ -f ipweb2_en.tar.gz ] ; then
/bin/tar xzf ipweb2_en.tar.gz
/bin/rm -r /www
/bin/mv ipweb2_en /www
/bin/rm ipweb2_en.tar.gz
fi

if [ -f ipweb3.tar.gz ] ; then
/bin/tar xzf ipweb3.tar.gz
/bin/cp -d /www/ipcfg.cgi /tmp/
/bin/rm -r /www
/bin/mv ipweb3 /www
/bin/mv /tmp/ipcfg.cgi /www
/bin/rm ipweb3.tar.gz
fi

touch -c /www/*.html

if [ -e /tmp/upver2 ] ; then
	/bin/rm /tmp/*.bin
else
	cd /home/<USER>
	/bin/rm *.bin
	/bin/rm update
fi

