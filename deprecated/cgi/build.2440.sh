#!/bin/sh

echo "Copy files ..."
# workspace change to here, no need to edit in /opt/share/cloud/Sync/SyncWork/Work/Workplace/webcfg/new/ any more
# cp /opt/share/cloud/Sync/SyncWork/Work/Workplace/webcfg/new/*.c .
# cp /opt/share/cloud/Sync/SyncWork/Work/Workplace/webcfg/new/*.h .
# cp /opt/share/cloud/Sync/SyncWork/Work/Workplace/webcfg/new/Makefile.2440 .
chmod -x *.c
chmod -x *.h
chmod -x Makefile.2440 
ln -sf Makefile.2440 Makefile

rm libcgic.a
ln -sf libcgic.a.2440 libcgic.a

echo "Build cgis ..."
make clean
make all

echo "Copy to target ..."
cp *.cgi /opt/share/cloud/syncall/SyncWork/linux/shell/ipsw3/2440/
cp *.cgi /opt/share/cloud/syncall/SyncWork/Work/release/tools/cgi/
mkdir -p cgi_2440
mv *.cgi cgi_2440/

rm *.o
rm src/*.o

#=============
#cp cgi/*.cgi ../ipweb3
cp cgi.2440/admin.cgi ../ipweb3
cp cgi.2440/down.cgi ../ipweb3
cp cgi.2440/ntpd.cgi ../ipweb3
cp cgi.2440/system.cgi ../ipweb3
#cp cgi/upgrade.cgi ../ipweb3

#make clean
echo "done!!!"

