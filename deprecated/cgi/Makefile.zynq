TOPDIR := .
DIR_SRC :=$(TOPDIR)/src
SRC := $(foreach DIR_SRC,$(DIR_SRC), $(wildcard $(DIR_SRC)/*.c))
DIR_HEADER := $(TOPDIR)/inc
OBJS :=$(patsubst %.c,%.o,$(SRC))

# compiler
CROSS := /opt/platform/zynq/petalinux/2018.3/tools/linux-i386/sdk/bin/arm-linux-gnueabihf-
CC := $(CROSS)gcc
CXX := $(CROSS)g++
LINK := $(CROSS)gcc
RANLIB :=$(CROSS)ranlib
STRIP=$(CROSS)strip
AR :=$(CROSS)ar

# flags
CFLAGS      := -Wall -O -g -I$(DIR_HEADER)
CPPFLAGS    := -Wall -O -g -I$(DIR_HEADER)
LINKFLAGS   := 
LIBS        :=-L./ -lcgic

#all: center.cgi gateway.cgi mini.cgi recorder.cgi scipub.cgi switchpub.cgi
#all: center.cgi mini.cgi scipub.cgi switchpub.cgi recorder.cgi
all: center.cgi gateway.cgi recorder.cgi mini.cgi switch3g.cgi sci3g.cgi switchpub.cgi scipub.cgi switch.cgi sci.cgi sci4g.cgi system.cgi ntpd.cgi admin.cgi
apps: center.cgi gateway.cgi recorder.cgi mini.cgi switch3g.cgi sci3g.cgi switchpub.cgi scipub.cgi switch.cgi sci.cgi sci4g.cgi
sysutils: system.cgi ntpd.cgi admin.cgi

center.cgi: 0center.o $(OBJS) libcgic.a
	$(CC) ${CFLAGS} -o center.cgi ${LIBS} $(OBJS) 0center.o
	$(STRIP) center.cgi

gateway.cgi: 0gateway.o $(OBJS) libcgic.a
	$(CC) ${CFLAGS} -o gateway.cgi ${LIBS} $(OBJS) 0gateway.o
	$(STRIP) gateway.cgi

recorder.cgi: 0recorder.o $(OBJS) libcgic.a
	$(CC) ${CFLAGS} -o recorder.cgi ${LIBS} $(OBJS) 0recorder.o
	$(STRIP) recorder.cgi

mini.cgi: 0mini.o $(OBJS) libcgic.a
	$(CC) ${CFLAGS} -o mini.cgi ${LIBS} $(OBJS) 0mini.o
	$(STRIP) mini.cgi

switch3g.cgi: 0switch3g.o $(OBJS) libcgic.a
	$(CC) ${CFLAGS} -o switch3g.cgi ${LIBS} $(OBJS) 0switch3g.o
	$(STRIP) switch3g.cgi

sci3g.cgi: 0sci3g.o $(OBJS) libcgic.a
	$(CC) ${CFLAGS} -o sci3g.cgi ${LIBS} $(OBJS) 0sci3g.o
	$(STRIP) sci3g.cgi

sci4g.cgi: 0sci4g.o $(OBJS) libcgic.a
	$(CC) ${CFLAGS} -o sci4g.cgi ${LIBS} $(OBJS) 0sci4g.o
	$(STRIP) sci4g.cgi

switchpub.cgi: 0switchpub.o $(OBJS) libcgic.a
	$(CC) ${CFLAGS} -o switchpub.cgi ${LIBS} $(OBJS) 0switchpub.o
	$(STRIP) switchpub.cgi

scipub.cgi: 0scipub.o $(OBJS) libcgic.a
	$(CC) ${CFLAGS} -o scipub.cgi ${LIBS} $(OBJS) 0scipub.o
	$(STRIP) scipub.cgi

switch.cgi: 0switch.o $(OBJS) libcgic.a
	$(CC) ${CFLAGS} -o switch.cgi ${LIBS} $(OBJS) 0switch.o
	$(STRIP) switchpub.cgi

sci.cgi: 0sci.o $(OBJS) libcgic.a
	$(CC) ${CFLAGS} -o sci.cgi ${LIBS} $(OBJS) 0sci.o
	$(STRIP) scipub.cgi

system.cgi: 0system.o $(OBJS) libcgic.a
	$(CC) ${CFLAGS} -o system.cgi ${LIBS} $(OBJS) 0system.o
	$(STRIP) system.cgi

ntpd.cgi: 0ntp.o $(OBJS) libcgic.a
	$(CC) ${CFLAGS} -o ntpd.cgi ${LIBS} $(OBJS) 0ntp.o
	$(STRIP) ntpd.cgi

admin.cgi: 0passwd.o $(OBJS) libcgic.a
	$(CC) ${CFLAGS} -o admin.cgi ${LIBS} $(OBJS) 0passwd.o
	$(STRIP) admin.cgi

######################################################
#center.cgi: 0center.o libcgic.a
#	$(CC) ${CFLAGS} -o center.cgi ${LIBS} 0center.o
#	$(STRIP) center.cgi

#center.cgi : $(OBJs)
#	@echo "center.cgi  ..."
#	$(LINK) $(LINKFLAGS) $(OBJs) $(LIBS) $^ -o $@
#######################################################

clean:
	rm -f *.o *.cgi


