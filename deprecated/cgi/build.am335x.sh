#!/bin/sh

echo "Copy files ..."
# workspace change to here, no need to edit in /opt/share/cloud/Sync/SyncWork/Work/Workplace/webcfg/new/ any more
# cp /opt/share/cloud/Sync/SyncWork/Work/Workplace/webcfg/new/*.c .
# cp /opt/share/cloud/Sync/SyncWork/Work/Workplace/webcfg/new/*.h .
# cp /opt/share/cloud/Sync/SyncWork/Work/Workplace/webcfg/new/Makefile.am335x .
chmod -x *.c
chmod -x *.h
chmod -x Makefile.am335x
ln -sf Makefile.am335x Makefile

rm libcgic.a
ln -sf libcgic.a.am335x libcgic.a

echo "Build cgis ..."
make clean
make all

echo "Copy to target ..."
cp *.cgi /opt/share/nfs/am335x/cgi/
cp *.cgi /opt/share/samba/work/cgi.am335x/
cp center.cgi gateway.cgi recorder.cgi mini.cgi switch3g.cgi sci3g.cgi switchpub.cgi scipub.cgi switch.cgi sci.cgi sci4g.cgi /opt/share/cloud/syncall/SyncWork/linux/shell/ipsw3/am335x/
cp *.cgi /opt/share/cloud/syncall/SyncWork/Work/release/tools/cgi.am335x/
mkdir -p cgi_am335x
mv *.cgi cgi_am335x/

#=============
# cp cgi.am335x/*.cgi ../ipweb3
cp cgi.am335x/admin.cgi ../ipweb3
cp cgi.am335x/down.cgi ../ipweb3
cp cgi.am335x/ntpd.cgi ../ipweb3
cp cgi.am335x/system.cgi ../ipweb3
# cp cgi.am335x/upgrade.cgi ../ipweb3

rm *.o
rm src/*.o

#make clean
echo "done!!!"

