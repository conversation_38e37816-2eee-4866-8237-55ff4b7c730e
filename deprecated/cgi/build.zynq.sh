#!/bin/sh

echo "Copy files ..."
# workspace change to here, no need to edit in /opt/share/cloud/Sync/SyncWork/Work/Workplace/webcfg/new/ any more
# cp /opt/share/cloud/Sync/SyncWork/Work/Workplace/webcfg/new/*.c .
# cp /opt/share/cloud/Sync/SyncWork/Work/Workplace/webcfg/new/*.h .
# cp /opt/share/cloud/Sync/SyncWork/Work/Workplace/webcfg/new/Makefile.zynq .
# chmod -x *.c
# chmod -x *.h
rm -rf Makefile
chmod -x Makefile.zynq
ln -sf Makefile.zynq Makefile

rm libcgic.a
ln -sf libcgic.a.zynq libcgic.a

echo "Build cgis ..."
make clean
make all

echo "Copy to target ..."
cp *.cgi /opt/share/nfs/zynq/cgi/
cp *.cgi /opt/share/samba/work/cgi.zynq/
cp center.cgi gateway.cgi recorder.cgi mini.cgi switch3g.cgi sci3g.cgi switchpub.cgi scipub.cgi switch.cgi sci.cgi sci4g.cgi /opt/share/cloud/syncall/SyncWork/linux/shell/ipsw3/zynq/
cp *.cgi /opt/share/cloud/syncall/SyncWork/Work/release/tools/cgi.zynq/
# actually just for down.cgi form old
mkdir -p cgi_zynq
mv *.cgi cgi_zynq/

#=============
# cp cgi.zynq/*.cgi ../ipweb3
cp cgi.zynq/admin.cgi ../ipweb3
cp cgi.zynq/down.cgi ../ipweb3
cp cgi.zynq/ntpd.cgi ../ipweb3
cp cgi.zynq/system.cgi ../ipweb3
# cp cgi.zynq/upgrade.cgi ../ipweb3

rm *.o
rm src/*.o

#make clean
echo "done!!!"

