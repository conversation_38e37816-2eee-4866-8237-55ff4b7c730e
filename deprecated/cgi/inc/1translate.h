/*******************************************************************
 * File          : 1translate.h
 * Author        : <PERSON><PERSON><PERSON>  <<EMAIL>>
 * Created       : 2017-02-28
 * Last modified : 2017-02-28
 *------------------------------------------------------------------
 * Description :
 * 
 *------------------------------------------------------------------
 * Modification history :
 * 2017-02-28 : created
 *******************************************************************/

#ifndef __1TRANSLATE_H__  
#define __1TRANSLATE_H__

/* SHOW LANGUAGE */
#define SHOW_LNG_SUM    2
#define SHOW_LNG_ZH     0
#define SHOW_LNG_EN     1

#define SHOW_LNG_MAX    SHOW_LNG_EN

#ifdef __cplusplus
extern "C"
{
#endif

extern int gLngType;

///////////////////////////////////////////////////////////////////////////////
extern const char *BTN_SAVE_NAME[SHOW_LNG_SUM]; 
extern const char *BTN_READ_NAME[SHOW_LNG_SUM]; 

extern const char *BTN_UPDATE_NAME[SHOW_LNG_SUM];
extern const char *BTN_REBOOT_NAME[SHOW_LNG_SUM];
extern const char *BTN_RESETCFG_NAME[SHOW_LNG_SUM]; 


extern const char *BTN_PWDUPDATE_NAME[SHOW_LNG_SUM];
/////////////////////////////////////////////////////////////////////////////// 

extern const char *LNG_WWAN_SETTING[SHOW_LNG_SUM];
extern const char *LNG_WWAN_CONFIG_NOT_FOUND[SHOW_LNG_SUM];
extern const char *LNG_WWAN_MODE[SHOW_LNG_SUM];
extern const char *LNG_WWAN_NOTUSE[SHOW_LNG_SUM];
extern const char *LNG_WWAN_DYMIP[SHOW_LNG_SUM]; 
extern const char *LNG_WWAN_NO_DYMIP[SHOW_LNG_SUM];
extern const char *LNG_WWAN_USE_VPDN[SHOW_LNG_SUM];
extern const char *LNG_WWAN_VPDN_USER[SHOW_LNG_SUM];
extern const char *LNG_WWAN_VPDN_PASSWD[SHOW_LNG_SUM];
extern const char *LNG_WWAN_VPDN_AUTH[SHOW_LNG_SUM];
extern const char *LNG_WWAN_VPDN_NOTUSE[SHOW_LNG_SUM]; 
extern const char *LNG_WWAN_USE_DDNS[SHOW_LNG_SUM]; 
extern const char *LNG_WWAN_DDNS_PROVIDER[SHOW_LNG_SUM];
extern const char *LNG_WWAN_DDNS_3322[SHOW_LNG_SUM];
extern const char *LNG_WWAN_DDNS_ORAY[SHOW_LNG_SUM];
extern const char *LNG_WWAN_DDNS_USER[SHOW_LNG_SUM];
extern const char *LNG_WWAN_DDNS_PASSWD[SHOW_LNG_SUM];
extern const char *LNG_WWAN_DDNS_URL[SHOW_LNG_SUM];
extern const char *LNG_WWAN_WRITE_SCRIPT_ERR[SHOW_LNG_SUM];
extern const char *LNG_WWAN_WRITE_PAP_ERR[SHOW_LNG_SUM];
extern const char *LNG_WWAN_WRITE_CHAP_ERR[SHOW_LNG_SUM];
extern const char *LNG_WWAN_WRITE_DIALING[SHOW_LNG_SUM];
extern const char *LNG_WWAN_WRITE_DDNS_ERR[SHOW_LNG_SUM];
extern const char *LNG_WWAN_WRITE_DDNS2_ERR[SHOW_LNG_SUM];
extern const char *LNG_WWAN_WRITE_DIAL2_ERR[SHOW_LNG_SUM];
extern const char *LNG_WWAN_P2P[SHOW_LNG_SUM];
extern const char *LNG_WWAN_P2P_LINK[SHOW_LNG_SUM];
extern const char *LNG_WWAN_P2P_STATIC[SHOW_LNG_SUM];
extern const char *LNG_WWAN_P2P_DDNS[SHOW_LNG_SUM];
extern const char *LNG_WWAN_P2P_WAITE[SHOW_LNG_SUM];
extern const char *LNG_WWAN_P2P_PEER[SHOW_LNG_SUM];
extern const char *LNG_WWAN_P2P_NOTE[SHOW_LNG_SUM];
extern const char *LNG_WWAN_P2P_LOCALPORT[SHOW_LNG_SUM];
extern const char *LNG_WWAN_P2P_REMOTEPORT[SHOW_LNG_SUM];
extern const char *LNG_WWAN_P2P_PORT_NOTE[SHOW_LNG_SUM];

extern const char *LNG_BASE_COMPATIBLE[SHOW_LNG_SUM];
extern const char *LNG_BASE_SIMULCAST_SYSCODE[SHOW_LNG_SUM];
extern const char *LNG_BASE_FREQUENCY_REG[SHOW_LNG_SUM];
extern const char *LNG_BASE_FREQUENCY[SHOW_LNG_SUM];

extern const char *LNG_CENTER_ADDRESS[SHOW_LNG_SUM];
extern const char *LNG_GATEWAY_ADDRESS[SHOW_LNG_SUM]; 
extern const char *LNG_CENTER_DS[SHOW_LNG_SUM];
extern const char *LNG_CENTER_SW[SHOW_LNG_SUM];
extern const char *LNG_CENTER_BS[SHOW_LNG_SUM];
extern const char *LNG_CENTER_NO[SHOW_LNG_SUM];
extern const char *LNG_CENTER_CALLOUTNO[SHOW_LNG_SUM];
extern const char *LNG_CENTER_CALLINNO[SHOW_LNG_SUM];
extern const char *LNG_CENTER_CALLINNO_SUM[SHOW_LNG_SUM];
extern const char *LNG_CENTER_PARAMETER[SHOW_LNG_SUM];
extern const char *LNG_GATEWAY_PARAMETER[SHOW_LNG_SUM]; 
extern const char *LNG_CENTER_SPEC_NO_GPS[SHOW_LNG_SUM];
extern const char *LNG_CENTER_SPEC_NOT_USE_VOCODER[SHOW_LNG_SUM];
extern const char *LNG_CENTER_SPEC_GET_FREE_CHAN_TYPE[SHOW_LNG_SUM];
extern const char *LNG_CENTER_SPEC_FREE_CHAN_TYPE_FIRST[SHOW_LNG_SUM];
extern const char *LNG_CENTER_SPEC_FREE_CHAN_TYPE_ROLL[SHOW_LNG_SUM];
extern const char *LNG_CENTER_VCHANSUM[SHOW_LNG_SUM];
extern const char *LNG_CENTER_VCHANSUM_NOTE[SHOW_LNG_SUM];
extern const char *LNG_CENTER_STARTPORT[SHOW_LNG_SUM];
extern const char *LNG_CENTER_DATAPORT[SHOW_LNG_SUM]; 
extern const char *LNG_CENTER_LINKTYPE[SHOW_LNG_SUM];
extern const char *LNG_CENTER_LINK_UNICAST[SHOW_LNG_SUM];
extern const char *LNG_CENTER_LINK_BROADCAST[SHOW_LNG_SUM];
extern const char *LNG_CENTER_LINK_MULTICAST[SHOW_LNG_SUM];
extern const char *LNG_CENTER_DISPATCH_IP[SHOW_LNG_SUM];
extern const char *LNG_CENTER_DISPATCH_PORT[SHOW_LNG_SUM];

extern const char *LNG_COM_PARAMETER[SHOW_LNG_SUM];
extern const char *LNG_COM_DS[SHOW_LNG_SUM];
extern const char *LNG_COM_SW[SHOW_LNG_SUM];
extern const char *LNG_COM_CONF_SUM[SHOW_LNG_SUM];

extern const char *LNG_BASIC_PARAMETER[SHOW_LNG_SUM];
extern const char *LNG_LOGIP_PARAMETER[SHOW_LNG_SUM];
extern const char *LNG_LOGPORT_PARAMETER[SHOW_LNG_SUM];

extern const char *LNG_CONF_PARAMETER[SHOW_LNG_SUM];
extern const char *LNG_CONF_FUNC[SHOW_LNG_SUM];
extern const char *LNG_CONF_ALWAYSSLAVE[SHOW_LNG_SUM];
extern const char *LNG_CONF_DUALBACKUP[SHOW_LNG_SUM];
extern const char *LNG_CONF_BACKUP[SHOW_LNG_SUM];
extern const char *LNG_CONF_ROLLCONF[SHOW_LNG_SUM];
extern const char *LNG_CONF_FADENOTREALSE[SHOW_LNG_SUM];
extern const char *LNG_CONF_SIMULCASET[SHOW_LNG_SUM];
extern const char *LNG_CONF_DYNAMIC_CHOOSE[SHOW_LNG_SUM];
extern const char *LNG_CONF_PARAMER[SHOW_LNG_SUM];
extern const char *LNG_CONF_PEERNUM[SHOW_LNG_SUM];
extern const char *LNG_CONF_VIP[SHOW_LNG_SUM];
extern const char *LNG_CONF_VPORT[SHOW_LNG_SUM];
extern const char *LNG_CONF_VNUM[SHOW_LNG_SUM];
extern const char *LNG_CONF_VOICE_PARAMETER[SHOW_LNG_SUM];
extern const char *LNG_CONF_BUFFERTIME[SHOW_LNG_SUM];
extern const char *LNG_CONF_FADETIME[SHOW_LNG_SUM];
extern const char *LNG_CONF_FADETIME_NOTE[SHOW_LNG_SUM];
extern const char *LNG_CONF_PEER_CFG[SHOW_LNG_SUM]; 
extern const char *LNG_CONF_PEERINDEX[SHOW_LNG_SUM];
extern const char *LNG_CONF_PEERIP[SHOW_LNG_SUM]; 
extern const char *LNG_CONF_PEERVIP[SHOW_LNG_SUM];
extern const char *LNG_CONF_PEERDATAPORT[SHOW_LNG_SUM];
extern const char *LNG_CONF_PEERVPORT[SHOW_LNG_SUM];
extern const char *LNG_CONF_PEER_ADDR[SHOW_LNG_SUM];
extern const char *LNG_CONF_PEERTYPE[SHOW_LNG_SUM];

extern const char *LNG_ETH_OPEN_FAILED[SHOW_LNG_SUM];
extern const char *LNG_ETH_SETTING[SHOW_LNG_SUM];
extern const char *LNG_ETH_IP[SHOW_LNG_SUM];
extern const char *LNG_ETH_IP_ERR[SHOW_LNG_SUM];
extern const char *LNG_ETH_MASK[SHOW_LNG_SUM];
extern const char *LNG_ETH_MASK_ERR[SHOW_LNG_SUM];
extern const char *LNG_ETH_GW[SHOW_LNG_SUM];
extern const char *LNG_ETH_GW_EER[SHOW_LNG_SUM];
extern const char *LNG_ETH_DNS[SHOW_LNG_SUM];
extern const char *LNG_ETH_DNS_ERR[SHOW_LNG_SUM];
extern const char *LNG_ETH_MAC[SHOW_LNG_SUM];
extern const char *LNG_ETH_MAC_ERR[SHOW_LNG_SUM];
extern const char *LNG_ETH_WLAN_SETTING[SHOW_LNG_SUM];
extern const char *LNG_ETH_WLAN[SHOW_LNG_SUM];
extern const char *LNG_ETH_WLAN_DISABLE[SHOW_LNG_SUM];
extern const char *LNG_ETH_WLAN_ENALBE[SHOW_LNG_SUM];
extern const char *LNG_ETH_USB_ENABLE[SHOW_LNG_SUM]; 

extern const char *LNG_NETWORK_CHOSEN[SHOW_LNG_SUM];
extern const char *LNG_NETWORK_ETH_ONLY[SHOW_LNG_SUM];
extern const char *LNG_NETWORK_WWAN_ONLY[SHOW_LNG_SUM];
extern const char *LNG_NETWORK_ETH_WWAN[SHOW_LNG_SUM];
extern const char *LNG_NETWORK_WWAN_ETH[SHOW_LNG_SUM];

extern const char *LNG_NETWORK_ETH_LINK[SHOW_LNG_SUM];
extern const char *LNG_NETWORK_WWAN_LINK[SHOW_LNG_SUM]; 

extern const char *LNG_NETWORK_ETH_CONNETING[SHOW_LNG_SUM];
extern const char *LNG_NETWORK_ADDRESS[SHOW_LNG_SUM];
extern const char *LNG_NETWORK_WWAN_CONNETING[SHOW_LNG_SUM];

extern const char *LNG_VOCODER_PARAMETER[SHOW_LNG_SUM];
extern const char *LNG_VOCODER_TYPE[SHOW_LNG_SUM]; 
extern const char *LNG_VOCODER_GAIN[SHOW_LNG_SUM];
extern const char *LNG_VOCODER_PCM[SHOW_LNG_SUM];
extern const char *LNG_VOCODER_PCM_LINEAR[SHOW_LNG_SUM];
extern const char *LNG_VOCODER_PCM_ALAW[SHOW_LNG_SUM];
extern const char *LNG_VOCODER_PCM_ULAW[SHOW_LNG_SUM];
extern const char *LNG_VOCODER_PCM_NOT[SHOW_LNG_SUM]; 
extern const char *LNG_VOCODER_PCM_PC[SHOW_LNG_SUM];


extern const char *LNG_VOCODER_SYSCODE[SHOW_LNG_SUM];
extern const char *LNG_VOCODER_SYSCODE_64KPCM[SHOW_LNG_SUM];
#if 0
extern const char *LNG_VOCODER_SYSCODE_PDT[SHOW_LNG_SUM];
extern const char *LNG_VOCODER_SYSCODE_N[SHOW_LNG_SUM];
extern const char *LNG_VOCODER_SYSCODE_Q[SHOW_LNG_SUM];
extern const char *LNG_VOCODER_SYSCODE_V75K[SHOW_LNG_SUM];
extern const char *LNG_VOCODER_SYSCODE_V25K[SHOW_LNG_SUM];
#endif
extern const char *LNG_VOCODER_NOFEC[SHOW_LNG_SUM]; 


extern const char *LNG_WEB_SAVE_SUCESS[SHOW_LNG_SUM];

extern const char *LNG_SYSTEM_BROWSER_NOTE[SHOW_LNG_SUM];
extern const char *LNG_SYSTEM_CFG_VER[SHOW_LNG_SUM];
extern const char *LNG_SYSTEM_HW_VER[SHOW_LNG_SUM];
extern const char *LNG_SYSTEM_APP_VER[SHOW_LNG_SUM];
extern const char *LNG_SYSTEM_SELECT_FIRST[SHOW_LNG_SUM];
extern const char *LNG_SYSTEM_FILE_NAME[SHOW_LNG_SUM];
extern const char *LNG_SYSTEM_REBOOT_NOTE[SHOW_LNG_SUM];
extern const char *LNG_SYSTEM_FILE_SIZE[SHOW_LNG_SUM];
extern const char *LNG_SYSTEM_SAVE_FAILED[SHOW_LNG_SUM];
extern const char *LNG_SYSTEM_RESETCFG_NOTE[SHOW_LNG_SUM]; 

extern const char *LNG_SYSTEM_NTP_SETTING[SHOW_LNG_SUM];
extern const char *LNG_SYSTEM_NTP_AS_SERVER[SHOW_LNG_SUM];
extern const char *LNG_SYSTEM_NTP_AS_CLIENT[SHOW_LNG_SUM];
extern const char *LNG_SYSTEM_NTP_SERVER[SHOW_LNG_SUM];
extern const char *LNG_SYSTEM_NTP_OPEN_FAILED[SHOW_LNG_SUM];
extern const char *LNG_SYSTEM_PWD_MISMATCH[SHOW_LNG_SUM];
extern const char *LNG_SYSTEM_PWD_UPDATE[SHOW_LNG_SUM];
extern const char *LNG_SYSTEM_PWD_NEW[SHOW_LNG_SUM];
extern const char *LNG_SYSTEM_PWD_COMFIRM[SHOW_LNG_SUM];
extern const char *LNG_SYSTEM_PWD_FAILED[SHOW_LNG_SUM];
extern const char *LNG_SYSTEM_PWD_SUCCESS[SHOW_LNG_SUM];

#ifdef __cplusplus
}
#endif

#endif /* __1TRANSLATE_H__ */
