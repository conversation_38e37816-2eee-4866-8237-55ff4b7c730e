#include "serialPort.h"
#include <termios.h>
#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <errno.h> 
#include <strings.h>
#include <sys/select.h>
#include <sys/time.h>
#include <string.h>

int name_arr[] = { 115200, 57600, 38400, 19200, 9600, 4800, 2400, 1200, 300 };
int speed_arr[] = { B115200, B57600, B38400, B19200, B9600, B4800, B2400, B1200, B300 }; /*波特率参数 */


struct termios termios_bak;	//备份串口参数


/**************** 打开串口 ***********************
* @传入参数：*serialPort 设备名
* @返回值：成功返回打开设备的文件描述符；失敗返回-1
************************************************/
int openSerialPort(const char *serialPort)
{
	int serialfd;

	serialfd = open(serialPort, O_RDWR|O_NOCTTY);	// O_NOCTTY: 如果打开的是一个终端设备，这个程序不会成为对应这个端口的控制终端.
				// O_NONBLOCK 以不可阻断的方式打开文件，也就是无论有无数据读取或等待，都会立即返回进程之中.

	if(serialfd < 0){
		perror(serialPort);	
		return -1;
	}

	if( fcntl(serialfd, F_SETFL, 0) < 0 ){	//恢复串口为阻塞状态
		perror("fcntl");	
		return -1;
	}

/*	if(isatty(serialfd)==0)		//测试是否为终端设备，是:1 ；不是:0。
	        printf("standard input is not a terminal device\n");
	else
        	printf("isatty success!\n");
*/
	return serialfd;	
}


/**************** 关闭串口 ***********************
* @传入参数：serialfd 已打开的设备的文件描述符
* @返回值：成功返回0；失败返回-1
************************************************/
int closeSerialPort(int serialfd)
{
	if( tcsetattr(serialfd, TCSADRAIN, &termios_bak)<0){	//恢复串口参数， TCSADRAIN:在所有写入 fd 的输出都被传输后改变属性
		perror("tcsetattr");
		return -1;
	}

	if( close(serialfd)<0 ){		//关闭串口
		perror("close");
		return -1;
	}
	return 0;
}

/********************************* 设置串口参数 ************************************
* @传入参数：serialfd 已打开的设备的文件描述符
* @传入参数：speed 波特率
* @传入参数：databits 数据位
* @传入参数：parity 较验方式
* @传入参数：stopbits 停止位
* @返回值：成功返回0，失败返回-1
**********************************************************************************/
#if 1
int setSerialPara(int serialfd, int speed, int databits, char parity, int stopbits)
{
	struct termios newtio;

	memset(&termios_bak, 0, sizeof(termios_bak));
	if( tcgetattr(serialfd, &termios_bak) < 0 ) {   //将原来串口参数保存到termios_bak结构体中.
		perror("tcgetattr");
		return -1;
	}

	memset(&newtio, 0, sizeof(newtio));
	cfmakeraw(&newtio);        //原始

	newtio.c_cflag |= CLOCAL | CREAD;      // CLOCAL:本地连线,忽略 modem 控制线;保证程序不会成为端口的占有者
															// CREAD:接收使能
	newtio.c_cflag &= ~CSIZE;

	/* set character size */
	switch( databits ) {
		case 8:
			newtio.c_cflag |= CS8;
			break;
		case 7:
			newtio.c_cflag |= CS7;
			break;
		case 6:
			newtio.c_cflag |= CS6;
			break;
		case 5:
			newtio.c_cflag |= CS5;
			break;
		default:
			newtio.c_cflag |= CS8;
			break;
	}

	/* set the parity */
	switch( parity ) {
		case 'o':
		case 'O':
			newtio.c_cflag |= PARENB;
			newtio.c_cflag |= PARODD;
			newtio.c_iflag |= (INPCK | ISTRIP);
			break;
		case 'e':
		case 'E':
			newtio.c_cflag |= PARENB;
			newtio.c_cflag &= ~PARODD;
			newtio.c_iflag |= (INPCK | ISTRIP);
			break;
		case 'n':
		case 'N':
			newtio.c_cflag &= ~PARENB;   /* Clear parity enable */
			newtio.c_iflag &= ~INPCK;     /* Enable parity checking */
			break;
		default:
			newtio.c_cflag &= ~PARENB;
			newtio.c_iflag &= ~INPCK;
			break;
	}

	/* set the stop bits */
	switch( stopbits ) {
		case 1:
			newtio.c_cflag &= ~CSTOPB;
			break;
		case 2:
			newtio.c_cflag |= CSTOPB;
			break;
		default:
			newtio.c_cflag &= ~CSTOPB;
			break;
	}

	/* set output and input baud rate */
	switch( speed )
	{
	case 0:
		cfsetospeed(&newtio, B0);
		cfsetispeed(&newtio, B0);
		break;
	case 50:
		cfsetospeed(&newtio, B50);
		cfsetispeed(&newtio, B50);
		break;
	case 75:
		cfsetospeed(&newtio, B75);
		cfsetispeed(&newtio, B75);
		break;
	case 110:
		cfsetospeed(&newtio, B110);
		cfsetispeed(&newtio, B110);
		break;
	case 134:
		cfsetospeed(&newtio, B134);
		cfsetispeed(&newtio, B134);
		break;
	case 150:
		cfsetospeed(&newtio, B150);
		cfsetispeed(&newtio, B150);
		break;
	case 200:
		cfsetospeed(&newtio, B200);
		cfsetispeed(&newtio, B200);
		break;
	case 300:
		cfsetospeed(&newtio, B300);
		cfsetispeed(&newtio, B300);
		break;
	case 600:
		cfsetospeed(&newtio, B600);
		cfsetispeed(&newtio, B600);
		break;
	case 1200:
		cfsetospeed(&newtio, B1200);
		cfsetispeed(&newtio, B1200);
		break;
	case 1800:
		cfsetospeed(&newtio, B1800);
		cfsetispeed(&newtio, B1800);
		break;
	case 2400:
		cfsetospeed(&newtio, B2400);
		cfsetispeed(&newtio, B2400);
		break;
	case 4800:
		cfsetospeed(&newtio, B4800);
		cfsetispeed(&newtio, B4800);
		break;
	case 9600:
		cfsetospeed(&newtio, B9600);
		cfsetispeed(&newtio, B9600);
		break;
	case 19200:
		cfsetospeed(&newtio, B19200);
		cfsetispeed(&newtio, B19200);
		break;
	case 38400:
		cfsetospeed(&newtio, B38400);
		cfsetispeed(&newtio, B38400);
		break;
	case 57600:
		cfsetospeed(&newtio, B57600);
		cfsetispeed(&newtio, B57600);
		break;
	case 115200:
		cfsetospeed(&newtio, B115200);
		cfsetispeed(&newtio, B115200);
		break;
	case 230400:
		cfsetospeed(&newtio, B230400);
		cfsetispeed(&newtio, B230400);
		break;
	case 921600:
		cfsetospeed(&newtio, B921600);
		cfsetispeed(&newtio, B921600);
		break;
	case 1000000:
		cfsetospeed(&newtio, B1000000);
		cfsetispeed(&newtio, B1000000);
		break;
	case 1500000:
		cfsetospeed(&newtio, B1500000);
		cfsetispeed(&newtio, B1500000);
		break;
	case 2000000:
		cfsetospeed(&newtio, B2000000);
		cfsetispeed(&newtio, B2000000);
		break;
	case 2500000:
		cfsetospeed(&newtio, B2500000);
		cfsetispeed(&newtio, B2500000);
		break;
	case 3000000:
		cfsetospeed(&newtio, B3000000);
		cfsetispeed(&newtio, B3000000);
		break;
	default:
		cfsetospeed(&newtio, B115200);
		cfsetispeed(&newtio, B115200);
	}
	newtio.c_cflag |= CLOCAL | CREAD;
	// ICRNL 将输入的回车转化成换行（如果IGNCR未设置的情况下）
	newtio.c_iflag &= ~(IGNCR | ICRNL);
	newtio.c_iflag &= ~(BRKINT | ISTRIP);
	newtio.c_iflag &= ~(IXON | IXOFF | IXANY);

	// newtio.c_lflag &= ~(ECHO | ICANON);
	newtio.c_lflag &= ~(ICANON | ECHO | ECHOE | ISIG);
	// newtio.c_lflag &= ~(ICANON | ISIG);

	/* set timeout in deciseconds for non-canonical read */
	newtio.c_cc[VTIME] = 0;
	/* set minimum number of characters for non-canonical read */
	newtio.c_cc[VMIN] = 10;

	/* flushes data received but not read */
	tcflush(serialfd, TCIFLUSH);
	/* set the parameters associated with the terminal from 
		the termios structure and the change occurs immediately */
	if( (tcsetattr(serialfd, TCSANOW, &newtio)) != 0 )
	{
		perror("set_port/tcsetattr");
		return -1;
	}
	
	return 0;
}
#else
int setSerialPara(int serialfd, int speed, int databits , char parity, int stopbits  )
{    

	struct termios termios_new; 
	
	memset(&termios_bak, 0, sizeof(termios_bak));
	if(tcgetattr(serialfd , &termios_bak)<0){	//将原来串口参数保存到termios_bak结构体中.
		perror("tcgetattr");
		return -1;
	}

	memset(&termios_new, 0, sizeof(termios_new));
	cfmakeraw(&termios_new);		//原始

	termios_new.c_cflag |= CLOCAL | CREAD;		// CLOCAL:本地连线,忽略 modem 控制线;保证程序不会成为端口的占有者
							// CREAD:接收使能
#ifdef MODE_TIMEOUT
	termios_new.c_cc[VTIME] = TIMEOUT;		//控制字符，读取第一个字符的等待时间unit:(1/10)second
	termios_new.c_cc[VMIN] = 0; 		//控制字符，所要读取字符的最小数量
#else
	termios_new.c_cc[VTIME] = 0;		//控制字符，读取第一个字符的等待时间unit:(1/10)second
	termios_new.c_cc[VMIN] = RCV_MIN; 		//控制字符，所要读取字符的最小数量
#endif

	unsigned int i;
	for ( i= 0;  i < sizeof(speed_arr) / sizeof(int);  i++) { 
		if  (speed == name_arr[i]) {     
			tcflush(serialfd, TCIOFLUSH);  
 
			if(cfsetispeed(&termios_new, speed_arr[i])<0){   //设置的输入波特率	
				perror("cfsetispeed");
				return -1;
			}			
			if(cfsetospeed(&termios_new, speed_arr[i])<0){   //设置输出波特率
				perror("cfsetospeed");			
				return -1;
			}
			tcflush(serialfd,TCIOFLUSH);
			break; 
		}  
	}
	if(i == sizeof(speed_arr)/sizeof(int) ){
		fprintf(stderr,"Unsupported Baudrate\n");
		return -1;
	}


	/*设置数据位长度*/
	termios_new.c_cflag &= ~CSIZE;		//先去除数据位掩码
	switch (databits) 	 	//设置数据位
	{
		case 8:
			termios_new.c_cflag |= CS8;
			break;
		case 7:
			termios_new.c_cflag |= CS7;
			break;
		case 6:
			termios_new.c_cflag |= CS6;
			break;
		case 5:
			termios_new.c_cflag |= CS5;
			break;	
		default:
			fprintf(stderr,"Unsupported data size\n");
			return -1;  
	}

	/*设置奇偶校验*/
	switch (parity) 	//设置奇偶校验	    
	{
		case 'n':      // 无奇偶校验位
		case 'N':    
			termios_new.c_cflag &= ~PARENB;   	//无奇偶校验 
			break;  
		case 'o':   
		case 'O':     
			termios_new.c_cflag |= (PARODD | PARENB); /* 设置为奇校验*/  
			termios_new.c_iflag |= INPCK;  // INPCK:奇偶校验使能

			break;  
		case 'e':  
		case 'E':   
			termios_new.c_cflag |= PARENB;     /* Enable parity */    
			termios_new.c_cflag &= ~PARODD;   /* 转换为偶效验*/     
			termios_new.c_iflag |= INPCK;     // INPCK:奇偶校验使能
			break;
		case 'S':    
		case 's':       
			termios_new.c_cflag &= ~PARENB;
			termios_new.c_cflag &= ~CSTOPB;
			termios_new.c_iflag |= INPCK;
			break;  
		default:   
			fprintf(stderr,"Unsupported parity\n");    
			return -1;  

	}
	
	/*设置停止位*/
	switch (stopbits)				// 设置停止位
	{
		case 1:
			termios_new.c_cflag &= ~CSTOPB;
			break;
   	 	case 2:
			termios_new.c_cflag |= CSTOPB;
			break;
   		default:
			fprintf(stderr,"Unsupported stop bits\n");
			return -1; 			
	}


	if( tcflush(serialfd, TCIFLUSH)<0 ){			//刷新输入数据但不读取
		perror("tcflush");
		return -1;
	}	

	if( tcsetattr(serialfd,TCSANOW,&termios_new) < 0 ){ 	//立即更新设置参数
		perror("tcsetattr");
		return -1;	
	}	

	return 0;
} 
#endif

/*********************** 读串口 *****************************
* @传入参数：serialfd 已打开的设备的文件描述符
* @传出参数：*buf 保存读取到的数据
* @传入参数: len 要读取的字节数 
* @返回值：成功返回读取到的字节数，超时返回0，出错返回-1
***********************************************************/
#if 1
#define TTY_READ_TIMEOUT_USEC	10000    // 5000
int readSerialPort(int fd, char *frame, int len) {
	struct timeval tv;
	fd_set fds;
	int ret;

	unsigned int timeout = TTY_READ_TIMEOUT_USEC;
	tv.tv_sec = 0;
	tv.tv_usec = timeout;

	FD_ZERO(&fds);
	FD_SET(fd, &fds);

	ret = select(fd + 1, &fds, NULL, NULL, &tv);
	if (ret < 0)
	{
		perror("select tty");
	}
	else if (ret > 0)
	{
		if (FD_ISSET(fd, &fds)) {
			ret = read(fd, frame, len);
//			WriteBinary((uint8_t *)frame, len);
		}
	}
	else
	{
		// select timeout
//		WriteDebug("select %d timeout", fd);
	}

	return ret;

}
#else
int readSerialPort(int serialfd, char *buf, int len)
{
	int index=0;
#ifdef MODE_TIMEOUT
	int read_size=0;
#endif
	bzero(buf, len );	

//	lseek( serialfd, 0, SEEK_SET ); /*将指针定为文件开头，偏移量为0*/
	tcflush(serialfd,TCIFLUSH);	

#ifdef MODE_TIMEOUT
	for( index = 0; index < len ; ){
		read_size = 0;
		if( (read_size = read(serialfd, &buf[index], len-index) ) > 0 ){
			index += read_size;
		}else{
			tcflush(serialfd,TCIFLUSH);	
			break;
		}
	}
	if( read_size == 0){
//		fprintf(stderr,"read:time out!\n");
		return 0;
	}
#else
	if( (index = read(serialfd, buf, len) ) < 0 ){
		perror("read");
		return -1;
	}
#endif

	return index;
}
#endif

/****************** 写串口 (未考虑写阻塞和超时设置)**************
* @传入参数：serialfd 已打开的设备的文件描述符
* @传入参数：*data 要写入的数据
* @传入参数: datalength 要写入的字节数 
* @返回值：成功返回写入的字节数=datalength
*****************************************************************/
int writeSerialPort (int serialfd , const char *data, int datalength)
{	
	int  len_wr, index;	//定义写入长度和总长度变量
	for (index = 0 ; index < datalength;){
		len_wr = 0;
		if( (len_wr = write(serialfd, &data[index], datalength-index)) > 0){	//写串口
			index += len_wr;
		}else{
			tcflush (serialfd, TCOFLUSH);	//刷新写入数据但不发送
			break;
		}	
	}

	return index;	//返回总长度
}

