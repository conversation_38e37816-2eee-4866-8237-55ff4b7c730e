/*******************************************************************
 * File          : 
 * Author        : Jin<PERSON><PERSON>g  <<EMAIL>>
 * Created       : 
 * Last modified : 
 *------------------------------------------------------------------
 * Description :
 * 
 *------------------------------------------------------------------
 * Modification history :
 *  : created
 *******************************************************************/

/** include files **/
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>

#include "cgic.h"
#include "0define.h"
#include "1utils.h"

#include "1rwcommon.h"

/** local definitions **/

/** default settings **/

/** external functions **/

/** external data **/

/** internal functions **/

/** public data **/

/** private data **/

/** public functions **/
/// common.cfg
void web_common_cfg_show(int err, stCfgCommon *pCommon)
{
    fprintf(cgiOut, "<hr>\n"); 
    fprintf(cgiOut, "<p><h3>%s</h3><p>\n", LNG_COM_PARAMETER[gLngType]); 
    fprintf(cgiOut, "%s<input type=\"text\" name=\"%s\" value=\"%d\">\n", LNG_COM_DS[gLngType], COMMON_KEY_DS, pCommon->ds);
    fprintf(cgiOut, "%s<input type=\"text\" name=\"%s\" value=\"%d\">\n", LNG_COM_SW[gLngType], COMMON_KEY_SW, pCommon->sw);
//  fprintf(cgiOut, "<p>\n");
    fprintf(cgiOut, "%s<input type=\"text\" name=\"%s\" value=\"%d\">\n", LNG_COM_CONF_SUM[gLngType], COMMON_KEY_CONFNUM, pCommon->conf_num);
    fprintf(cgiOut, "<p>\n");
}

int web_common_cfg_get(stCfgCommon *pCommon)
{
    int val;

    cgiFormIntegerBounded(COMMON_KEY_DS, &val, 0, 63, 1);
    pCommon->ds = val & 0xFF;
    cgiFormIntegerBounded(COMMON_KEY_SW, &val, 0, 255, 1);
    pCommon->sw = val & 0xFF; 
    cgiFormIntegerBounded(COMMON_KEY_CONFNUM, &val, 1, 96, 48);     // 022217 ���������ĳ�96
    pCommon->conf_num = val & 0xFF;
    
    // default
    pCommon->normal_num = 0;

    PrintDebug("Common ds=%d, sw=%d, cn=%d<br>\n", pCommon->ds, pCommon->sw, pCommon->conf_num);

    return 0;
}

void set_default_value_commoncfg(stCfgCommon *pCfg)
{
    PrintDebug("set_default_value_commoncfg<br>\n");

    pCfg->ds = 10;
    pCfg->sw = 1;
    pCfg->conf_num = 96;
    pCfg->normal_num = 0;
}
/** private functions **/
