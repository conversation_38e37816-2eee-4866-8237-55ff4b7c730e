/*******************************************************************
 * File          : 1rwccenter.c
 * Author        : <PERSON><PERSON><PERSON>  <<EMAIL>>
 * Created       : 2016-05-27
 * Last modified : 2016-05-27
 *------------------------------------------------------------------
 * Description :
 * 
 *------------------------------------------------------------------
 * Modification history :
 * 2016-05-27 : created
 *******************************************************************/

/** include files **/
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>

#include <errno.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <netdb.h>

#include "cgic.h"
#include "0define.h"
#include "1utils.h"

#include "1rwcenter.h"

#define _VCHANSUM_SETABLE_    1

/** local definitions **/

/** default settings **/

/** external functions **/

/** external data **/

/** internal functions **/

/** public data **/

/** private data **/

/** public functions **/
/// callcenter.cfg
void web_callcenter_cfg_show(int err, stCfgCenter *pCenter)
{
#if _SHOW_ALL_
    struct in_addr ipaddr;
#endif
    char usrid[8];

    fprintf(cgiOut, "<hr>\n"); 

    fprintf(cgiOut, "<p><h3>%s</h3><p>\n", LNG_CENTER_ADDRESS[gLngType]);
    fprintf(cgiOut, "%s<input type=\"text\" name=\"%s\" value=\"%d\">\n", LNG_CENTER_DS[gLngType], CENTER_KEY_DS, GETDS(pCenter->center_no));
    fprintf(cgiOut, "%s<input type=\"text\" name=\"%s\" value=\"%d\">\n", LNG_CENTER_SW[gLngType], CENTER_KEY_SW, GETSW(pCenter->center_no));
    fprintf(cgiOut, "%s<input type=\"text\" name=\"%s\" value=\"%d\">\n", LNG_CENTER_BS[gLngType], CENTER_KEY_BS, GETBS(pCenter->center_no));
    fprintf(cgiOut, "<p>\n");
    fprintf(cgiOut, "<hr>\n");

    fprintf(cgiOut, "<p><h3>%s</h3><p>\n", LNG_CENTER_NO[gLngType]);
    radionum_ssi2str(usrid, pCenter->center_outssi, 0); 
    fprintf(cgiOut, "%s<input type=\"text\" name=\"%s\" value=\"%s\">\n", LNG_CENTER_CALLOUTNO[gLngType], CENTER_KEY_OUTSSI, usrid);
    fprintf(cgiOut, "<p>\n");
    radionum_ssi2str(usrid, pCenter->center_inssi, 0); 
    fprintf(cgiOut, "%s<input type=\"text\" name=\"%s\" value=\"%s\">\n", LNG_CENTER_CALLINNO[gLngType], CENTER_KEY_INSSI, usrid);
    fprintf(cgiOut, "%s<input type=\"text\" name=\"%s\" value=\"%d\">\n", LNG_CENTER_CALLINNO_SUM[gLngType], CENTER_KEY_INSSISUM, pCenter->inssi_num);
    fprintf(cgiOut, "<p>\n");

    fprintf(cgiOut, "<hr>\n"); 
    fprintf(cgiOut, "<p><h3>%s</h3><p>\n", LNG_CENTER_PARAMETER[gLngType]);

    // pCenter->spec_function #1 选项
    fprintf(cgiOut, "<input type=\"checkbox\" name=\"%s\" value=\"A\" %s>%s\n", CENTER_KEY_SPEC_FUNC, (pCenter->spec_function & (1)) ? "checked" : "", LNG_CENTER_SPEC_NO_GPS[gLngType]);
    fprintf(cgiOut, "<input type=\"checkbox\" name=\"%s\" value=\"B\" %s>%s\n", CENTER_KEY_SPEC_FUNC, (pCenter->spec_function & (1 << 1)) ? "checked" : "", LNG_CENTER_SPEC_NOT_USE_VOCODER[gLngType]);
    fprintf(cgiOut, "<p>\n");

#if (_VCHANSUM_SETABLE_ || _SHOW_ALL_)
    fprintf(cgiOut, "%s<input type=\"text\" name=\"%s\" value=\"%d\">\n", LNG_CENTER_VCHANSUM[gLngType], CENTER_KEY_VCHANSUM, pCenter->vchan_sum);
//  fprintf(cgiOut, "<p>\n");
    fprintf(cgiOut, "%s\n", LNG_CENTER_VCHANSUM_NOTE[gLngType]);
    fprintf(cgiOut, "<p>\n");
    // 音频通道获取方式
    fprintf(cgiOut, "%s\n", LNG_CENTER_SPEC_GET_FREE_CHAN_TYPE[gLngType]);
    fprintf(cgiOut, "<input type=\"radio\" name=\"%s\" value=\"0\" %s>%s\n", CENTER_KEY_SPEC_FUNC_VCHAN, ((pCenter->spec_function & (1 << 2)) == 0) ? "checked" : "", LNG_CENTER_SPEC_FREE_CHAN_TYPE_FIRST[gLngType]);
    fprintf(cgiOut, "<input type=\"radio\" name=\"%s\" value=\"1\" %s>%s\n", CENTER_KEY_SPEC_FUNC_VCHAN, ((pCenter->spec_function & (1 << 2)) == 0x04) ? "checked" : "", LNG_CENTER_SPEC_FREE_CHAN_TYPE_ROLL[gLngType]);
    fprintf(cgiOut, "<p>\n"); 
#endif

#if _SHOW_ALL_
    fprintf(cgiOut, "%s<input type=\"text\" name=\"%s\" value=\"%d\">\n", LNG_CENTER_STARTPORT[gLngType], CENTER_KEY_VPORT, pCenter->center_voice_port);
    fprintf(cgiOut, "%s<input type=\"text\" name=\"%s\" value=\"%d\">\n", LNG_CENTER_DATAPORT[gLngType], CENTER_KEY_DPORT, pCenter->listen_agent_port);
    fprintf(cgiOut, "<p>\n");
    fprintf(cgiOut, "%s<p>\n", LNG_CENTER_LINKTYPE[gLngType]);
    fprintf(cgiOut, "<input type=\"radio\" name=\"%s\" value=\"1\" %s>%s\n", CENTER_KEY_PEERNET, (pCenter->peer_net_type == 0) ? "checked" : "", LNG_CENTER_LINK_UNICAST[gLngType]);
    fprintf(cgiOut, "<input type=\"radio\" name=\"%s\" value=\"0\" %s>%s\n", CENTER_KEY_PEERNET, (pCenter->peer_net_type == 1) ? "checked" : "", LNG_CENTER_LINK_BROADCAST[gLngType]);
    fprintf(cgiOut, "<input type=\"radio\" name=\"%s\" value=\"1\" %s>%s\n", CENTER_KEY_PEERNET, (pCenter->peer_net_type == 2) ? "checked" : "", LNG_CENTER_LINK_MULTICAST[gLngType]);
    ipaddr.s_addr = htonl(pCenter->send_all_agent_ip); 
    fprintf(cgiOut, "%s<input type=\"text\" name=\"%s\" value=\"%s\">\n", LNG_CENTER_DISPATCH_IP[gLngType], CENTER_KEY_PEERIP, inet_ntoa(ipaddr));
    fprintf(cgiOut, "<p>\n");
    fprintf(cgiOut, "%s<input type=\"text\" name=\"%s\" value=\"%d\">\n", LNG_CENTER_DISPATCH_PORT[gLngType], CENTER_KEY_PEERPORT, pCenter->send_to_agent_port);
    fprintf(cgiOut, "<p>\n");
#endif
}


void web_gateway_cfg_show(int err, stCfgCenter *pCenter)
{
#if _SHOW_ALL_
    struct in_addr ipaddr;
#endif
#if 0
    char usrid[8];
#endif

    fprintf(cgiOut, "<hr>\n");

    fprintf(cgiOut, "<p><h3>%s</h3><p>\n", LNG_GATEWAY_ADDRESS[gLngType]);
    fprintf(cgiOut, "%s<input type=\"text\" name=\"%s\" value=\"%d\">\n", LNG_CENTER_DS[gLngType], CENTER_KEY_DS, GETDS(pCenter->center_no));
    fprintf(cgiOut, "%s<input type=\"text\" name=\"%s\" value=\"%d\">\n", LNG_CENTER_SW[gLngType], CENTER_KEY_SW, GETSW(pCenter->center_no));
    fprintf(cgiOut, "%s<input type=\"text\" name=\"%s\" value=\"%d\">\n", LNG_CENTER_BS[gLngType], CENTER_KEY_BS, GETBS(pCenter->center_no));
    fprintf(cgiOut, "<p>\n");
    fprintf(cgiOut, "<hr>\n");

#if 0
    fprintf(cgiOut, "<p><h3>%s</h3><p>\n", LNG_CENTER_NO[gLngType]);
    radionum_ssi2str(usrid, pCenter->center_outssi, 0);
    fprintf(cgiOut, "%s<input type=\"text\" name=\"%s\" value=\"%s\">\n", LNG_CENTER_CALLOUTNO[gLngType], CENTER_KEY_OUTSSI, usrid);
    fprintf(cgiOut, "<p>\n");
    radionum_ssi2str(usrid, pCenter->center_inssi, 0);
    fprintf(cgiOut, "%s<input type=\"text\" name=\"%s\" value=\"%s\">\n", LNG_CENTER_CALLINNO[gLngType], CENTER_KEY_INSSI, usrid);
    fprintf(cgiOut, "%s<input type=\"text\" name=\"%s\" value=\"%d\">\n", LNG_CENTER_CALLINNO_SUM[gLngType], CENTER_KEY_INSSISUM, pCenter->inssi_num);
    fprintf(cgiOut, "<p>\n");
    fprintf(cgiOut, "<hr>\n");
#endif

    fprintf(cgiOut, "<p><h3>%s</h3><p>\n", LNG_GATEWAY_PARAMETER[gLngType]);

    // pCenter->spec_function #1 选项
    fprintf(cgiOut, "<input type=\"checkbox\" name=\"%s\" value=\"A\" %s>%s\n", CENTER_KEY_SPEC_FUNC, (pCenter->spec_function & (1)) ? "checked" : "", LNG_CENTER_SPEC_NO_GPS[gLngType]);
    fprintf(cgiOut, "<input type=\"checkbox\" name=\"%s\" value=\"B\" %s>%s\n", CENTER_KEY_SPEC_FUNC, (pCenter->spec_function & (1 << 1)) ? "checked" : "", LNG_CENTER_SPEC_NOT_USE_VOCODER[gLngType]);
    fprintf(cgiOut, "<p>\n");

#if (_VCHANSUM_SETABLE_ || _SHOW_ALL_)
    fprintf(cgiOut, "%s<input type=\"text\" name=\"%s\" value=\"%d\">\n", LNG_CENTER_VCHANSUM[gLngType], CENTER_KEY_VCHANSUM, pCenter->vchan_sum);
//  fprintf(cgiOut, "<p>\n");
    fprintf(cgiOut, "%s\n", LNG_CENTER_VCHANSUM_NOTE[gLngType]);
    fprintf(cgiOut, "<p>\n");

    // 音频通道获取方式
    fprintf(cgiOut, "%s\n", LNG_CENTER_SPEC_GET_FREE_CHAN_TYPE[gLngType]);
    fprintf(cgiOut, "<input type=\"radio\" name=\"%s\" value=\"0\" %s>%s\n", CENTER_KEY_SPEC_FUNC_VCHAN, ((pCenter->spec_function & (1 << 2)) == 0) ? "checked" : "", LNG_CENTER_SPEC_FREE_CHAN_TYPE_FIRST[gLngType]);
    fprintf(cgiOut, "<input type=\"radio\" name=\"%s\" value=\"1\" %s>%s\n", CENTER_KEY_SPEC_FUNC_VCHAN, ((pCenter->spec_function & (1 << 2)) == 0x04) ? "checked" : "", LNG_CENTER_SPEC_FREE_CHAN_TYPE_ROLL[gLngType]);
    fprintf(cgiOut, "<p>\n"); 
#endif


#if _SHOW_ALL_
    fprintf(cgiOut, "%s<input type=\"text\" name=\"%s\" value=\"%d\">\n", LNG_CENTER_STARTPORT[gLngType], CENTER_KEY_VPORT, pCenter->center_voice_port);
    fprintf(cgiOut, "%s<input type=\"text\" name=\"%s\" value=\"%d\">\n", LNG_CENTER_DATAPORT[gLngType], CENTER_KEY_DPORT, pCenter->listen_agent_port);
    fprintf(cgiOut, "<p>\n");
    fprintf(cgiOut, "%s<p>\n", LNG_CENTER_LINKTYPE[gLngType]);
    fprintf(cgiOut, "<input type=\"radio\" name=\"%s\" value=\"1\" %s>%s\n", CENTER_KEY_PEERNET, (pCenter->peer_net_type == 0) ? "checked" : "", LNG_CENTER_LINK_UNICAST[gLngType]);
    fprintf(cgiOut, "<input type=\"radio\" name=\"%s\" value=\"0\" %s>%s\n", CENTER_KEY_PEERNET, (pCenter->peer_net_type == 1) ? "checked" : "", LNG_CENTER_LINK_BROADCAST[gLngType]);
    fprintf(cgiOut, "<input type=\"radio\" name=\"%s\" value=\"1\" %s>%s\n", CENTER_KEY_PEERNET, (pCenter->peer_net_type == 2) ? "checked" : "", LNG_CENTER_LINK_MULTICAST[gLngType]);
    ipaddr.s_addr = htonl(pCenter->send_all_agent_ip);
    fprintf(cgiOut, "%s<input type=\"text\" name=\"%s\" value=\"%s\">\n", LNG_CENTER_DISPATCH_IP[gLngType], CENTER_KEY_PEERIP, inet_ntoa(ipaddr));
    fprintf(cgiOut, "<p>\n");
    fprintf(cgiOut, "%s<input type=\"text\" name=\"%s\" value=\"%d\">\n", LNG_CENTER_DISPATCH_PORT[gLngType], CENTER_KEY_PEERPORT, pCenter->send_to_agent_port);
    fprintf(cgiOut, "<p>\n");
#endif
}

#define SPEC_FUNC_SUM   2
int web_callcenter_cfg_get(stCfgCenter *pCenter)
{
    int val;
#if _SHOW_ALL_
    char *peertype[] =
    { "0", "1", "2" };
    char peerip[LEN_IP_ADDR];
#endif
    char ssi[9];
    int ds, sw, bs;

    int invalid;
    int sfv[SPEC_FUNC_SUM + 1];
    char *sf[] = { "A", "B" };
    char *vchanv[] = { "0", "1" }; 

    cgiFormIntegerBounded(CENTER_KEY_DS, &ds, 1, 63, 1);
    cgiFormIntegerBounded(CENTER_KEY_SW, &sw, 1, 255, 1);
    cgiFormIntegerBounded(CENTER_KEY_BS, &bs, 1, 31, 29);
    pCenter->center_no = MAKEADDR(ds, sw, bs, 28);

    cgiFormStringNoNewlines(CENTER_KEY_OUTSSI, ssi, 9);
    pCenter->center_outssi = radionum_str2ssi(ssi, 0);

    cgiFormStringNoNewlines(CENTER_KEY_INSSI, ssi, 9);
    pCenter->center_inssi = radionum_str2ssi(ssi, 0);

    cgiFormIntegerBounded(CENTER_KEY_INSSISUM, &val, 0, 200, 10);       // 240116 set mini to 0
    pCenter->inssi_num = val & 0xFFFF;

    pCenter->spec_function = 0;
    val = cgiFormCheckboxMultiple(CENTER_KEY_SPEC_FUNC, sf, SPEC_FUNC_SUM, sfv, &invalid);
    if(val == cgiFormSuccess)
    {
        int i;
        for(i = 0; i < SPEC_FUNC_SUM; i++)
        {
            pCenter->spec_function |= sfv[i] << i;
        }
    }

#if ((_VCHANSUM_SETABLE_) || (_SHOW_ALL_))

    cgiFormIntegerBounded(CENTER_KEY_VCHANSUM, &val, 1, ((pCenter->spec_function & (1 << 1))?31 : 10), 10);
    pCenter->vchan_sum = val & 0xFF;

    val = 0;
    cgiFormRadio(CENTER_KEY_SPEC_FUNC_VCHAN, vchanv, 2, &val, 0);
    pCenter->spec_function |= (val & 0x01) << 2;
    PrintDebug("------spec_function=%02X<br>\n", pCenter->spec_function);
#endif

#if _SHOW_ALL_

    cgiFormIntegerBounded(CENTER_KEY_VPORT, &val, 1, 65535, 2800);
    pCenter->center_voice_port = val & 0xFFFF;
    cgiFormIntegerBounded(CENTER_KEY_DPORT, &val, 1, 65535, 2702);
    pCenter->listen_agent_port = val & 0xFFFF;

    cgiFormRadio(CENTER_KEY_PEERNET, peertype, 3, &val, 0);
    pCenter->peer_net_type = val & 0xFF;
    cgiFormStringNoNewlines(CENTER_KEY_PEERIP, peerip, LEN_IP_ADDR_);
    peerip[LEN_IP_ADDR - 1] = 0;
    pCenter->send_all_agent_ip = ntohl(inet_addr(peerip));

    cgiFormIntegerBounded(CENTER_KEY_PEERPORT, &val, 1, 65535, 2704);
    pCenter->send_to_agent_port = val & 0xFFFF;
#endif

    return 0;
}

int web_callcenter_cfg_get2(stCfgCenter *pCenter)
{
    int val;
    int ds, sw, bs;
    char ssi[9];

    int invalid;
    int sfv[SPEC_FUNC_SUM + 1];
    char *sf[] = { "A", "B" };
    char *vchanv[] = { "0", "1" }; 

    cgiFormIntegerBounded(CENTER_KEY_DS, &ds, 1, 63, 1);
    cgiFormIntegerBounded(CENTER_KEY_SW, &sw, 1, 255, 1);
    cgiFormIntegerBounded(CENTER_KEY_BS, &bs, 1, 31, 29);
    pCenter->center_no = MAKEADDR(ds, sw, bs, 28);

    cgiFormStringNoNewlines(CENTER_KEY_OUTSSI, ssi, 9);
    pCenter->center_outssi = radionum_str2ssi(ssi, 0);

    cgiFormStringNoNewlines(CENTER_KEY_INSSI, ssi, 9);
    pCenter->center_inssi = radionum_str2ssi(ssi, 0);

    cgiFormIntegerBounded(CENTER_KEY_INSSISUM, &val, 0, 200, 10);       // 240116 set mini to 0
    pCenter->inssi_num = val & 0xFFFF;

    // default
    pCenter->center_voice_port = FUNCTION_VOICE_PEER_PORT_BASE;
    pCenter->listen_agent_port = CALLCENTER_RECV_AGENT_PORT;
    pCenter->peer_net_type = ePeerNetUnicast;
    pCenter->send_all_agent_ip = ntohl(inet_addr("*********"));
    pCenter->send_to_agent_port = CALLCENTER_AGENT_RECV_PORT;

    pCenter->spec_function = 0;
    val = cgiFormCheckboxMultiple(CENTER_KEY_SPEC_FUNC, sf, SPEC_FUNC_SUM, sfv, &invalid);
    if(val == cgiFormSuccess)
    {
        int i;
        for(i = 0; i < SPEC_FUNC_SUM; i++)
        {
            pCenter->spec_function |= sfv[i] << i;
        }
    }
    
#if ((_VCHANSUM_SETABLE_) || (_SHOW_ALL_))

    cgiFormIntegerBounded(CENTER_KEY_VCHANSUM, &val, 1, ((pCenter->spec_function & (1 << 1)) ? MAX_NOVOCODER_CHAN : 20), 20);
    pCenter->vchan_sum = val & 0xFF;

    val = 0;
    cgiFormRadio(CENTER_KEY_SPEC_FUNC_VCHAN, vchanv, 2, &val, 0);
    pCenter->spec_function |= (val & 0x01) << 2;
    PrintDebug("------spec_function=%02X<br>\n", pCenter->spec_function);
#else
    pCenter->vchan_sum = MAX_VOCODER_CHAN;
//  pCenter->spec_function |= (0 & 0x01) << 2;
#endif

    return 0;
}

int web_gateway_cfg_get2(stCfgCenter *pCenter)
{
    int val;
    int ds, sw, bs;
#if 0
    char ssi[9];
#endif

    int invalid;
    int sfv[SPEC_FUNC_SUM + 1];
    char *sf[] = { "A", "B" };
    char *vchanv[] = { "0", "1" }; 

    cgiFormIntegerBounded(CENTER_KEY_DS, &ds, 1, 63, 1);
    cgiFormIntegerBounded(CENTER_KEY_SW, &sw, 1, 255, 1);
    cgiFormIntegerBounded(CENTER_KEY_BS, &bs, 1, 31, 29);
    pCenter->center_no = MAKEADDR(ds, sw, bs, 28);

#if 0
    cgiFormStringNoNewlines(CENTER_KEY_OUTSSI, ssi, 9);
    pCenter->center_outssi = radionum_str2ssi(ssi, 0);

    cgiFormStringNoNewlines(CENTER_KEY_INSSI, ssi, 9);
    pCenter->center_inssi = radionum_str2ssi(ssi, 0);

    cgiFormIntegerBounded(CENTER_KEY_INSSISUM, &val, 1, 200, 10);
    pCenter->inssi_num = val & 0xFFFF;
#else
    pCenter->center_outssi = 0x1002B1;   // 1040362=模拟号的8170    0xFC7EE1
    pCenter->center_inssi = 0x1002B1;    // 1040362=模拟号的8170    0xFC02B1==888
    pCenter->inssi_num = 0; 
#endif
    // default
    pCenter->center_voice_port = FUNCTION_VOICE_PEER_PORT_BASE;
    pCenter->listen_agent_port = CALLCENTER_RECV_AGENT_PORT;
    pCenter->peer_net_type = ePeerNetUnicast;
    pCenter->send_all_agent_ip = ntohl(inet_addr("*********"));
    pCenter->send_to_agent_port = CALLCENTER_AGENT_RECV_PORT;

    pCenter->spec_function = 0;
    val = cgiFormCheckboxMultiple(CENTER_KEY_SPEC_FUNC, sf, SPEC_FUNC_SUM, sfv, &invalid);
    if(val == cgiFormSuccess)
    {
        int i;
        for(i = 0; i < SPEC_FUNC_SUM; i++)
        {
            pCenter->spec_function |= sfv[i] << i;
        }
    }

#if ((_VCHANSUM_SETABLE_) || (_SHOW_ALL_))
    //  cgiFormIntegerBounded(CENTER_KEY_VCHANSUM, &val, 1, 31, 10);
    cgiFormIntegerBounded(CENTER_KEY_VCHANSUM, &val, 1, ((pCenter->spec_function & (1 << 1)) ? MAX_NOVOCODER_CHAN : 10), 10);
    pCenter->vchan_sum = val & 0xFF;

    val = 0;
    cgiFormRadio(CENTER_KEY_SPEC_FUNC_VCHAN, vchanv, 2, &val, 0);
    pCenter->spec_function |= (val & 0x01) << 2;
    PrintDebug("------spec_function=%02X<br>\n", pCenter->spec_function);
#else
    pCenter->vchan_sum = MAX_VOCODER_CHAN;
//  pCenter->spec_function |= (0 & 0x01) << 2;
#endif

    return 0;
}

void set_default_value_center(stCfgCenter *pCenter)
{
    PrintDebug("set_default_value_center<br>\n");

    pCenter->center_no = MAKEADDR(10, 1, 28, 28);
    pCenter->center_outssi = 0xFC02B1;   // 1040362=模拟号的8170    0xFC7EE1
    pCenter->center_inssi = 0xFC02B1;    // 1040362=模拟号的8170    0xFC02B1==888
    pCenter->inssi_num = 10;
    pCenter->center_voice_port = FUNCTION_VOICE_PEER_PORT_BASE;
    pCenter->listen_agent_port = CALLCENTER_RECV_AGENT_PORT;
    pCenter->vchan_sum = MAX_VOCODER_CHAN;
    pCenter->peer_net_type = ePeerNetUnicast;
    pCenter->send_all_agent_ip = ntohl(inet_addr("*********"));
    pCenter->send_to_agent_port = CALLCENTER_AGENT_RECV_PORT;
    pCenter->spec_function = 0;
}
/** private functions **/


