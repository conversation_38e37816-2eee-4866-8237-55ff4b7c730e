/*******************************************************************
 * File          : 1rwweb.c
 * Author        : <PERSON><PERSON><PERSON>  <<EMAIL>>
 * Created       : 2016-05-07
 * Last modified : 2016-05-07
 *------------------------------------------------------------------
 * Description :
 * Web base function
 *------------------------------------------------------------------
 * Modification history :
 * 2016-05-07 : created
 *******************************************************************/

/** include files **/
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include "cgic.h"

#include "0define.h"
#include "1utils.h"

#include "1rwweb.h"

/** local definitions **/

/** default settings **/

/** external functions **/

/** external data **/

/** internal functions **/

/** public data **/

/** private data **/

/** public functions **/

void web_head(const char *title)
{
    /* Set any new cookie requested. Must be done *before*
         outputting the content type. */
    //CookieSet();
    /* Send the content type, letting the browser know this is HTML */
    cgiHeaderContentType("text/html");
    /* Top of the page */
    fprintf(cgiOut, "<HTML><HEAD>\n");
    fprintf(cgiOut, "<TITLE>%s</TITLE>\n", title);
    fprintf(cgiOut, "<link href=\"CSS/weide.css\" rel=\"stylesheet\" type=\"text/css\" />\n");
    fprintf(cgiOut, "</HEAD>\n"); 
}

void web_body_head(void)
{
    fprintf(cgiOut, "<BODY>\n");
    fprintf(cgiOut, "<table width=100%% border=0 cellspacing=0 cellpadding=0>\n");
    fprintf(cgiOut, "<tr><td class=\"bodytext12\">\n");
    fprintf(cgiOut, "<!-- 2.0: multipart/form-data is required for file uploads. -->\n");
    fprintf(cgiOut, "<form method=\"POST\" enctype=\"multipart/form-data\" ");
    fprintf(cgiOut, "action=\"");
    cgiValueEscape(cgiScriptName);
    fprintf(cgiOut, "\">\n");
}

void web_body_tail(void)
{
    fprintf(cgiOut, "</form>\n");
    fprintf(cgiOut, "</td></tr>\n");
    fprintf(cgiOut, "</table>\n");
    fprintf(cgiOut, "</BODY></HTML>\n");
}

void web_hr_show(void)
{
    fprintf(cgiOut, "<hr>\n");
}

void web_button_show(const char *btn, const char *name)
{
    fprintf(cgiOut, "<input type=\"submit\" name=\"%s\" value=\"%s\">\n", btn, name);
}

void web_info_save_ok(void)
{
    fprintf(cgiOut, "Save configurations OK!");
}

void web_info_open_err(char *pfilename)
{
    fprintf(cgiOut, LNG_WEB_OPEN_ERROR, pfilename);
}

void web_info_input_err(int errno)
{
    fprintf(cgiOut, LNG_WEB_INPUT_ERROR, errno);
}


///////////////////////////////////////////////////////////////////////////////
/// status show?
void web_status_show(void)
{
}

///////////////////////////////////////////////////////////////////////////////
cgiFormResultType web_click_button(char *btn)
{
    return cgiFormSubmitClicked(btn);
}

///////////////////////////////////////////////////////////////////////////////

/** private functions **/


