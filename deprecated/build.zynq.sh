#! /bin/sh
cd ./old/
./build.sh
cd ../

cd ./new/
./build.sh
cd ..

cat <<EOF > update
#! /bin/sh

date -s `date +%Y%m%d%H%M`
./update_all
EOF
chmod +x update

cd /opt/share/cloud/syncall/SyncWork/linux/shell/webcfg

tar czf ipweb.tar.gz ipweb
tar czf ipweb.bin ipweb.tar.gz update update_all
mv ipweb.bin ipweb.arm.bin

tar czf ipweb2.tar.gz ipweb2
tar czf ipweb2.bin ipweb2.tar.gz update update_all
mv ipweb2.bin ipweb2.arm.bin

tar czf ipweb3.tar.gz ipweb3
tar czf ipweb3.bin ipweb3.tar.gz update update_all
mv ipweb3.bin ipweb3.arm.bin

#cp *.bin /opt/share/nfs/Work/tools/web/
mkdir -p /opt/share/cloud/syncall/SyncWork/Work/release/tools/web/$(date +%Y%m%d)
cp *.bin /opt/share/cloud/syncall/SyncWork/Work/release/tools/web/$(date +%Y%m%d)
mv *.bin /opt/share/samba/work/tools/
rm *.tar.gz
rm update
