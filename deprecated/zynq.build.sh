#! /bin/sh

cd ./old/
./build.zynq.sh
cd ../

cd ./new/
./build.zynq.sh
cd ../

cat <<EOF > update
#! /bin/sh

date -s `date +%Y%m%d%H%M`
./update_all
EOF
chmod +x update

cd /opt/share/cloud/syncall/SyncWork/linux/shell/webcfg

tar czf ipweb3.tar.gz ipweb3
tar czf ipweb3.bin ipweb3.tar.gz update update_all

vtenc ipweb3.bin
rm ipweb3.bin
mv ipweb3.bin.enc ipweb3.zynq.bin

# cp *.bin /opt/share/nfs/Work/tools/web.zynq/
mkdir -p /opt/share/cloud/syncall/SyncWork/Work/release/tools/web/$(date +%Y%m%d)
cp *.bin /opt/share/cloud/syncall/SyncWork/Work/release/tools/web/$(date +%Y%m%d)
mv *.bin /opt/share/samba/work/tools.zynq/

rm *.tar.gz
rm update
