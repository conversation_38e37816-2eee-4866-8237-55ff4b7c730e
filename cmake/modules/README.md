# 第三方库管理系统模块

## 模块结构

```
cmake/
├── ThirdPartyManager.cmake     # 主入口，协调各个模块
└── modules/
    ├── ThirdPartyConfig.cmake  # 第三方库配置定义
    ├── ThirdPartyUtils.cmake   # 通用工具函数
    ├── AutotoolsBuilder.cmake  # Autotools构建器
    └── CMakeBuilder.cmake      # CMake构建器
```

## 模块职责

### ThirdPartyManager.cmake
- 主入口文件，引入所有子模块
- 提供统一的第三方库处理接口
- 协调构建流程和缓存管理

### ThirdPartyConfig.cmake
- 定义支持的第三方库列表
- 配置每个库的构建方式（autotools/cmake）
- 管理库的依赖关系和链接要求

### ThirdPartyUtils.cmake
- 提供通用工具函数
- 缓存管理（哈希计算、缓存检查）
- 导入目标创建
- 信息显示和清理功能

### AutotoolsBuilder.cmake
- 专门处理使用autotools的库（如microhttpd）
- 支持autogen.sh自动运行
- 交叉编译支持
- 配置参数管理

### CMakeBuilder.cmake
- 专门处理使用CMake的库（如cjson、cgic）
- 参数传递和配置
- 库特定构建选项

## 使用方法

### 在主CMakeLists.txt中：
```cmake
include(cmake/ThirdPartyManager.cmake)
handle_3rdparty_libraries()
```

### 添加新库：
1. 在ThirdPartyConfig.cmake中添加库名到THIRD_PARTY_LIBS
2. 在get_library_build_type()中定义构建类型
3. 如有需要，在对应构建器中添加库特定配置

### 清理缓存：
```cmake
clean_3rdparty_cache()
```

### 显示信息：
```cmake
show_3rdparty_info()
show_3rdparty_config()
```

## 设计优势

1. **模块化**：功能分离，便于维护
2. **扩展性**：添加新库只需修改配置
3. **通用性**：支持多种构建系统
4. **缓存机制**：避免重复构建
5. **交叉编译**：自动处理不同平台 