# webcfg 安装配置
# 按照优化后的目录结构进行安装

# 设置安装路径
set(WEBCFG_DIST_COMMON_DIR "${WEBCFG_DIST_DIR}/common")
set(WEBCFG_DIST_PLATFORM_DIR "${WEBCFG_DIST_DIR}/${TARGET_PLATFORM}")

message(STATUS "安装配置:")
message(STATUS "  公共资源目录: ${WEBCFG_DIST_COMMON_DIR}")
message(STATUS "  平台目录: ${WEBCFG_DIST_PLATFORM_DIR}")

# 创建安装目录
install(CODE "
    file(MAKE_DIRECTORY \"${WEBCFG_DIST_COMMON_DIR}/include\")
    file(MAKE_DIRECTORY \"${WEBCFG_DIST_COMMON_DIR}/web\")
    file(MAKE_DIRECTORY \"${WEBCFG_DIST_PLATFORM_DIR}/bin\")
    file(MAKE_DIRECTORY \"${WEBCFG_DIST_PLATFORM_DIR}/lib\")
")

# 安装项目头文件（平台无关）
if(EXISTS ${CMAKE_SOURCE_DIR}/include)
    install(DIRECTORY ${CMAKE_SOURCE_DIR}/include/
        DESTINATION ${WEBCFG_DIST_COMMON_DIR}/include
        FILES_MATCHING PATTERN "*.h"
        PATTERN "CMakeFiles" EXCLUDE
    )
endif()

# 安装第三方库头文件（平台无关）
foreach(LIB_NAME ${THIRD_PARTY_LIBS})
    set(LIB_INCLUDE_DIR "${WEBCFG_CACHE_DIR}/${LIB_NAME}/include")
    if(EXISTS ${LIB_INCLUDE_DIR})
        install(DIRECTORY ${LIB_INCLUDE_DIR}/
            DESTINATION ${WEBCFG_DIST_COMMON_DIR}/include
            FILES_MATCHING PATTERN "*.h"
        )
    endif()
endforeach()

# 安装Web资源（平台无关）
if(EXISTS ${CMAKE_SOURCE_DIR}/web)
    install(DIRECTORY ${CMAKE_SOURCE_DIR}/web/
        DESTINATION ${WEBCFG_DIST_COMMON_DIR}/web
        FILES_MATCHING 
        PATTERN "*.html"
        PATTERN "*.css"
        PATTERN "*.js"
        PATTERN "*.png"
        PATTERN "*.jpg"
        PATTERN "*.ico"
        PATTERN "*.gif"
        PATTERN "*.svg"
    )
endif()

# 安装主程序（平台特定）
install(TARGETS webcfg-server
    RUNTIME DESTINATION ${WEBCFG_DIST_PLATFORM_DIR}/bin
)

# 安装第三方库静态库文件（平台特定）
foreach(LIB_NAME ${THIRD_PARTY_LIBS})
    set(LIB_FILE "${WEBCFG_CACHE_DIR}/${LIB_NAME}/lib/lib${LIB_NAME}.a")
    if(EXISTS ${LIB_FILE})
        install(FILES ${LIB_FILE}
            DESTINATION ${WEBCFG_DIST_PLATFORM_DIR}/lib
        )
    endif()
endforeach()

# 创建平台信息文件
configure_file(
    ${CMAKE_SOURCE_DIR}/cmake/platform_info.txt.in
    ${CMAKE_BINARY_DIR}/platform_info.txt
    @ONLY
)
install(FILES ${CMAKE_BINARY_DIR}/platform_info.txt
    DESTINATION ${WEBCFG_DIST_PLATFORM_DIR}
)

# 创建README文件
configure_file(
    ${CMAKE_SOURCE_DIR}/cmake/install_readme.txt.in
    ${CMAKE_BINARY_DIR}/README.txt
    @ONLY
)
install(FILES ${CMAKE_BINARY_DIR}/README.txt
    DESTINATION ${WEBCFG_DIST_DIR}
)

# 创建启动脚本（如果需要）
if(TARGET_PLATFORM STREQUAL "native")
    configure_file(
        ${CMAKE_SOURCE_DIR}/cmake/start_server.sh.in
        ${CMAKE_BINARY_DIR}/start_server.sh
        @ONLY
    )
    install(FILES ${CMAKE_BINARY_DIR}/start_server.sh
        DESTINATION ${WEBCFG_DIST_PLATFORM_DIR}/bin
        PERMISSIONS OWNER_READ OWNER_WRITE OWNER_EXECUTE
                   GROUP_READ GROUP_EXECUTE
                   WORLD_READ WORLD_EXECUTE
    )
endif()

# 显示安装信息
install(CODE "
    message(STATUS \"\\n=== 安装完成 ===\")
    message(STATUS \"安装位置: ${WEBCFG_DIST_DIR}\")
    message(STATUS \"主程序: ${WEBCFG_DIST_PLATFORM_DIR}/bin/webcfg-server\")
    message(STATUS \"头文件: ${WEBCFG_DIST_COMMON_DIR}/include/\")
    message(STATUS \"Web资源: ${WEBCFG_DIST_COMMON_DIR}/web/\")
    message(STATUS \"库文件: ${WEBCFG_DIST_PLATFORM_DIR}/lib/\")
    message(STATUS \"\\n要运行程序，请执行:\")
    message(STATUS \"${WEBCFG_DIST_PLATFORM_DIR}/bin/webcfg-server\")
") 