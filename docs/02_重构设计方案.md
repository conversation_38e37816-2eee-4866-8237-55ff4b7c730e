# 重构方案设计文档

## 1. 重构目标与约束

### 1.1 重构核心目标
- **100%功能兼容**: 保持所有现有功能不变，**严禁增加任何新功能**
- **前后端分离**: 使用libmicrohttpd替代cgic，提供RESTful API接口
- **现代化界面**: 使用HTML5+CSS3替代frameset布局，保持相同功能
- **构建系统升级**: 使用cmake替代多个重复的Makefile
- **多平台支持**: 保持对am335x、zynq、s3c2440三个平台的支持
- **模块化设计**: 将重复代码提取为公共模块，**不改变功能边界**
- **代码重复消除**: 通过统一接口减少60%的重复代码

### 1.2 严格约束条件
- **功能约束**: 15个CGI程序对应15个API端点，功能一一对应
- **配置兼容性**: 配置文件格式、存储路径、数据结构完全兼容
- **业务逻辑约束**: 保持业务逻辑不变，**严禁增加复杂的业务规则**
- **界面约束**: 支持中英文界面切换，保持相同的功能布局
- **构建约束**: 第三方库静态编译，放置在3rdparty目录
- **复杂度约束**: **新系统复杂度不能超过旧系统**
- **平台约束**: 必须支持原有的三个ARM平台交叉编译

### 1.3 CGI程序与API端点严格对应

| 旧CGI程序 | 新API端点 | 代码行数 | 功能描述 | 严格约束 |
|-----------|-----------|----------|----------|----------|
| 0center.c | /api/v1/config/center | 419行 | 呼叫中心配置读写 | 保持所有配置结构体字段 |
| 0gateway.c | /api/v1/config/gateway | 410行 | 网关配置读写 | 与center共享相同结构体 |
| 0recorder.c | /api/v1/config/recorder | 中等 | 录音配置读写 | 包含对等基站配置 |
| 0mini.c | /api/v1/config/mini | 中等 | 迷你基站配置读写 | 与recorder类似结构 |
| 0sci.c | /api/v1/config/sci | 252行 | SCI基站配置读写 | 基站控制器配置 |
| 0sci3g.c | /api/v1/config/sci3g | 中等 | SCI基站3G配置读写 | 3G网络环境配置 |
| 0sci4g.c | /api/v1/config/sci4g | 中等 | SCI基站4G配置读写 | 4G网络环境配置 |
| 0switch.c | /api/v1/config/switch | 289行 | 交换机配置读写 | 交换机设备配置 |
| 0switch3g.c | /api/v1/config/switch3g | 中等 | 交换机3G配置读写 | 3G网络环境配置 |
| 0switchpub.c | /api/v1/config/switchpub | 中等 | 公用交换机配置读写 | 公共交换机配置 |
| 0scipub.c | /api/v1/config/scipub | 中等 | 公用SCI基站配置读写 | 公共SCI基站配置 |
| 0system.c | /api/v1/system/* | 102行 | 系统管理（重启、重置） | **极简实现**：仅3个操作 |
| 0ntp.c | /api/v1/config/ntp | 简单 | NTP时间同步配置 | 时间同步服务配置 |
| 0passwd.c | /api/v1/auth/password | 简单 | 密码管理 | 管理员密码修改 |
| 0down.c | /api/v1/system/logs,signal | 329行 | 日志查看和3G信号检测 | 串口AT命令检测 |

## 2. 项目目录结构设计

### 2.1 总体目录结构

```
webcfg_mod/
├── src/                    # C源代码
│   ├── main.c             # 入口点，HTTP服务器设置
│   ├── api/               # API路由系统
│   │   ├── api_router.c   # 统一路由管理
│   │   └── api_router.h   # 路由接口定义
│   ├── config/            # 配置模块（严格对应15个CGI程序）
│   │   ├── center_config.c      # 呼叫中心配置（对应0center.c）
│   │   ├── gateway_config.c     # 网关配置（对应0gateway.c）
│   │   ├── recorder_config.c    # 录音配置（对应0recorder.c）
│   │   ├── mini_config.c        # 迷你基站配置（对应0mini.c）
│   │   ├── sci_config.c         # SCI基站配置（对应0sci.c）
│   │   ├── sci3g_config.c       # SCI基站3G配置（对应0sci3g.c）
│   │   ├── sci4g_config.c       # SCI基站4G配置（对应0sci4g.c）
│   │   ├── switch_config.c      # 交换机配置（对应0switch.c）
│   │   ├── switch3g_config.c    # 交换机3G配置（对应0switch3g.c）
│   │   ├── switchpub_config.c   # 公用交换机配置（对应0switchpub.c）
│   │   ├── scipub_config.c      # 公用SCI基站配置（对应0scipub.c）
│   │   └── ntp_config.c         # NTP时间同步配置（对应0ntp.c）
│   ├── system/                  # 系统管理（对应0system.c+0down.c）
│   │   ├── system_management.c  # 系统管理：重启、重置、上传
│   │   └── system_logs.c        # 日志查看和信号检测
│   ├── auth/              # 认证模块（对应0passwd.c）
│   │   └── auth_handler.c # 密码管理
│   └── utils/             # 工具库（复用旧项目1rw*.c功能）
│       ├── config_utils.c       # 配置文件读写工具
│       ├── network_utils.c      # 网络工具函数
│       ├── file_utils.c         # 文件操作工具
│       └── json_utils.c         # JSON处理工具
├── web/                   # Web前端
│   ├── index.html         # 单页应用入口
│   ├── js/                # 模块化JavaScript
│   │   ├── api.js                 # HTTP客户端与认证
│   │   ├── router.js              # 单页路由
│   │   ├── auth-manager.js        # 认证状态管理
│   │   ├── ui-core.js             # 统一UI组件
│   │   └── pages/                 # 页面处理器（严格对应15个CGI）
│   │       ├── center-config.js        # 呼叫中心配置
│   │       ├── gateway-config.js       # 网关配置
│   │       ├── recorder-config.js      # 录音配置
│   │       ├── mini-config.js          # 迷你基站配置
│   │       ├── sci-config.js           # SCI基站配置
│   │       ├── sci3g-config.js         # SCI基站3G配置
│   │       ├── sci4g-config.js         # SCI基站4G配置
│   │       ├── switch-config.js        # 交换机配置
│   │       ├── switch3g-config.js      # 交换机3G配置
│   │       ├── switchpub-config.js     # 公用交换机配置
│   │       ├── scipub-config.js        # 公用SCI基站配置
│   │       ├── ntp-config.js           # NTP时间同步配置
│   │       ├── system-management.js    # 系统管理
│   │       ├── system-logs.js          # 日志查看和信号检测
│   │       └── auth-config.js          # 认证配置
│   └── css/               # 样式和响应式设计
│       ├── main.css       # 主样式文件
│       └── responsive.css # 响应式布局
├── 3rdparty/              # 第三方库（静态编译）
│   ├── libmicrohttpd/     # HTTP服务器库（替代cgic）
│   └── cjson/             # JSON解析库
├── cmake/                 # 构建配置和工具链
│   ├── toolchains/        # 交叉编译工具链配置
│   │   ├── am335x.cmake   # AM335x平台工具链
│   │   ├── zynq.cmake     # Zynq平台工具链
│   │   └── s3c2440.cmake  # S3C2440平台工具链
│   └── modules/           # CMake模块
├── scripts/               # 构建和部署脚本
│   ├── build.sh           # 统一构建脚本
│   └── deploy.sh          # 部署脚本
├── install/               # 安装配置
│   └── systemd/           # systemd服务配置
└── docs/                  # 文档
    ├── 01_项目分析报告.md
    ├── 02_重构设计方案.md
    └── 03_重构实施步骤.md
```

### 2.2 模块化设计原则

#### 2.2.1 配置模块统一接口
每个配置模块严格对应一个旧CGI程序，功能边界完全一致：

```c
// 统一配置模块接口
typedef struct {
    const char *module_name;           // 模块名称
    const char *config_file_path;      // 配置文件路径
    size_t config_struct_size;         // 配置结构大小
    int (*load_config)(void *config);  // 加载配置
    int (*save_config)(void *config);  // 保存配置
    int (*validate_config)(void *config); // 配置验证
} config_module_t;
```

#### 2.2.2 API处理统一模式
所有API处理函数遵循统一模式：

```c
// 统一API处理函数签名
typedef int (*api_handler_t)(struct MHD_Connection *connection,
                            const char *url,
                            const char *method,
                            const char *upload_data,
                            size_t *upload_data_size);

// 配置API处理函数命名规范
int handle_center_config(struct MHD_Connection *connection, ...);
int handle_gateway_config(struct MHD_Connection *connection, ...);
```

## 3. 命名规范

### 3.1 文件和目录命名规范

#### 3.1.1 目录命名
- **源码目录**：小写字母和下划线，如 `src/`, `config/`, `utils/`
- **模块目录**：按功能模块命名，如 `config/`, `system/`, `auth/`
- **第三方库目录**：使用原库名，如 `3rdparty/libmicrohttpd/`, `3rdparty/cjson/`

#### 3.1.2 文件命名
- **C源文件**：模块名_config.c，如 `center_config.c`, `gateway_config.c`
- **C头文件**：模块名_config.h，如 `center_config.h`, `gateway_config.h`
- **配置文件**：保持与旧项目文件名完全一致
- **脚本文件**：功能描述.sh，如 `build.sh`, `deploy.sh`

### 3.2 代码命名规范

#### 3.2.1 函数命名
- **API处理函数**：`handle_模块_config`，如 `handle_center_config()`, `handle_gateway_config()`
- **配置操作函数**：`模块_config_操作`，如 `center_config_load()`, `center_config_save()`
- **工具函数**：`工具类型_操作`，如 `config_utils_read()`, `network_utils_validate()`
- **系统函数**：`system_操作`，如 `system_reboot()`, `system_reset()`

#### 3.2.2 变量命名
- **全局变量**：`g_变量名`，如 `g_server_port`, `g_config_path`
- **静态变量**：`s_变量名`，如 `s_api_routes`, `s_config_modules`
- **结构体成员**：小写字母和下划线，如 `server_ip`, `listen_port`
- **宏定义**：全大写字母和下划线，如 `MAX_CONFIG_SIZE`, `DEFAULT_PORT`

#### 3.2.3 结构体和类型命名
- **配置结构体**：保持与旧项目完全一致，如 `stCfgCenter`, `stCfgCommon`
- **API结构体**：`api_功能_t`，如 `api_route_t`, `api_response_t`
- **工具结构体**：`utils_功能_t`，如 `file_utils_t`, `json_utils_t`

#### 3.2.4 API路径命名（严格对应15个CGI）
- **配置API**：`/api/v1/config/模块名`
  - `/api/v1/config/center` (对应0center.c)
  - `/api/v1/config/gateway` (对应0gateway.c)
  - `/api/v1/config/recorder` (对应0recorder.c)
  - `/api/v1/config/mini` (对应0mini.c)
  - `/api/v1/config/sci` (对应0sci.c)
  - `/api/v1/config/sci3g` (对应0sci3g.c)
  - `/api/v1/config/sci4g` (对应0sci4g.c)
  - `/api/v1/config/switch` (对应0switch.c)
  - `/api/v1/config/switch3g` (对应0switch3g.c)
  - `/api/v1/config/switchpub` (对应0switchpub.c)
  - `/api/v1/config/scipub` (对应0scipub.c)
  - `/api/v1/config/ntp` (对应0ntp.c)
- **系统API**：`/api/v1/system/操作` (对应0system.c+0down.c)
  - `/api/v1/system/reboot`
  - `/api/v1/system/reset`
  - `/api/v1/system/upload`
  - `/api/v1/system/logs`
  - `/api/v1/system/signal`
- **认证API**：`/api/v1/auth/操作` (对应0passwd.c)
  - `/api/v1/auth/login`
  - `/api/v1/auth/logout`
  - `/api/v1/auth/password`

## 4. 总体架构设计

### 4.1 架构概览

```
┌─────────────────────────────────────────────────────────────┐
│                    前端层 (Web Frontend)                     │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   HTML5 SPA     │ │   CSS3 Styles   │ │   JavaScript    │ │
│  │  (替代frameset)  │ │  (现代化布局)    │ │  (模块化设计)    │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                               │ HTTP/JSON RESTful API
                               ▼
┌─────────────────────────────────────────────────────────────┐
│                  HTTP服务层 (HTTP Service)                   │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │  libmicrohttpd  │ │   API Router    │ │  JSON Handler   │ │
│  │  (替代cgic)      │ │  (统一路由)      │ │  (数据处理)      │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                               │
                               ▼
┌─────────────────────────────────────────────────────────────┐
│                 配置管理层 (Config Management)               │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │ 15个配置模块     │ │  系统管理模块    │ │  认证管理模块    │ │
│  │ (对应15个CGI)    │ │ (对应0system.c)  │ │ (对应0passwd.c)  │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                               │
                               ▼
┌─────────────────────────────────────────────────────────────┐
│                 数据存储层 (Data Storage)                    │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │  Binary Config  │ │   INI Config    │ │  System Files   │ │
│  │  (二进制配置)    │ │  (INI配置)      │ │  (系统文件)      │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 4.2 分层设计详述

#### 4.2.1 前端层 (Web Frontend)
- **技术栈**: HTML5 + CSS3 + JavaScript (原生，无框架)
- **架构**: 单页应用 (SPA) 替代frameset布局
- **路由**: 基于hash的客户端路由，对应15个配置页面
- **交互**: 基于fetch API的异步通信，替代表单提交
- **样式**: 响应式设计，保持工业设备界面风格

#### 4.2.2 HTTP服务层 (HTTP Service)
- **技术栈**: C语言 + libmicrohttpd HTTP服务器
- **协议**: RESTful API + JSON数据格式
- **认证**: 基于session token的身份验证
- **路由**: 统一的API路由管理，15个配置API + 5个系统API + 3个认证API
- **功能约束**: **严格对应旧CGI程序功能，不增加新接口**

#### 4.2.3 配置管理层 (Config Management)
- **复用策略**: 最大化复用现有1rw*.c代码，**不增加新的业务规则**
- **重构范围**: 仅重构接口层，**保持核心逻辑完全不变**
- **模块化**: 15个配置模块对应15个CGI程序，**功能边界完全一致**
- **简单性**: **保持旧项目的简单性，不引入复杂的业务流程**

#### 4.2.4 数据存储层 (Data Storage)
- **兼容性**: 完全保持现有配置文件格式，**不增加新的配置项**
- **封装**: 提供统一的配置读写接口，**功能与旧CGI的文件操作一致**
- **事务性**: 保证配置操作的原子性，**但不增加复杂的事务管理**

## 5. 构建系统设计

### 5.1 CMake构建系统架构

#### 5.1.1 主项目CMake配置
```cmake
cmake_minimum_required(VERSION 3.12)
project(webcfg_system C)

# 设置C标准
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_STANDARD_REQUIRED ON)

# 编译选项
set(CMAKE_C_FLAGS "-Wall -O2")
set(CMAKE_C_FLAGS_DEBUG "-O0 -g3 -DDEBUG")
set(CMAKE_C_FLAGS_RELEASE "-O3 -DNDEBUG")

# 平台检测
if(NOT DEFINED TARGET_PLATFORM)
    set(TARGET_PLATFORM "native")
endif()

# 包含工具链配置
if(NOT TARGET_PLATFORM STREQUAL "native")
    include(cmake/toolchains/${TARGET_PLATFORM}.cmake)
endif()

# 第三方库路径
set(THIRDPARTY_DIR ${CMAKE_SOURCE_DIR}/3rdparty)

# 子目录
add_subdirectory(src)
```

#### 5.1.2 第三方库管理策略
```cmake
# 第三方库静态编译配置
function(add_static_library name)
    set(LIB_DIR ${THIRDPARTY_DIR}/${name})

    # libmicrohttpd (使用autotools)
    if(name STREQUAL "libmicrohttpd")
        ExternalProject_Add(${name}
            SOURCE_DIR ${LIB_DIR}
            CONFIGURE_COMMAND ${LIB_DIR}/configure --enable-static --disable-shared
            BUILD_COMMAND make
            INSTALL_COMMAND ""
        )
    endif()

    # cjson (使用cmake)
    if(name STREQUAL "cjson")
        ExternalProject_Add(${name}
            SOURCE_DIR ${LIB_DIR}
            CMAKE_ARGS -DENABLE_CJSON_TEST=OFF -DBUILD_SHARED_LIBS=OFF
        )
    endif()
endfunction()

# 添加所需的第三方库
add_static_library(libmicrohttpd)
add_static_library(cjson)
```

### 5.2 跨平台构建支持

#### 5.2.1 支持的平台（基于实际分析）
- **native**: 本地开发 (x86_64 Linux)
- **am335x**: TI AM335x ARM Cortex-A8
- **zynq**: Xilinx Zynq ARM Cortex-A9
- **s3c2440**: Samsung S3C2440 ARM920T

#### 5.2.2 交叉编译工具链配置
```cmake
# cmake/toolchains/am335x.cmake
set(CMAKE_SYSTEM_NAME Linux)
set(CMAKE_SYSTEM_PROCESSOR arm)
set(CMAKE_C_COMPILER arm-linux-gnueabihf-gcc)
set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)

# cmake/toolchains/zynq.cmake
set(CMAKE_SYSTEM_NAME Linux)
set(CMAKE_SYSTEM_PROCESSOR arm)
set(CMAKE_C_COMPILER arm-linux-gnueabihf-gcc)

# cmake/toolchains/s3c2440.cmake
set(CMAKE_SYSTEM_NAME Linux)
set(CMAKE_SYSTEM_PROCESSOR arm)
set(CMAKE_C_COMPILER arm-linux-gcc)
```

### 5.3 构建脚本

#### 5.3.1 统一构建脚本 (scripts/build.sh)
```bash
#!/bin/bash
PLATFORM=${1:-native}
BUILD_TYPE=${2:-Debug}

mkdir -p build/${PLATFORM}
cd build/${PLATFORM}

if [ "$PLATFORM" != "native" ]; then
    cmake -DTARGET_PLATFORM=${PLATFORM} \
          -DCMAKE_BUILD_TYPE=${BUILD_TYPE} \
          -DCMAKE_TOOLCHAIN_FILE=../../cmake/toolchains/${PLATFORM}.cmake \
          ../..
else
    cmake -DCMAKE_BUILD_TYPE=${BUILD_TYPE} ../..
fi

make -j$(nproc)
```

#### 5.3.2 使用示例
```bash
# 本地开发构建
./scripts/build.sh

# 交叉编译构建
./scripts/build.sh am335x Release
./scripts/build.sh zynq Debug
./scripts/build.sh s3c2440 Release
```

## 6. 统一API接口设计

### 6.1 API接口规范

#### 6.1.1 RESTful API设计原则
- **资源导向**: 每个API端点对应一个配置资源
- **HTTP方法**: GET获取配置，POST保存配置
- **统一格式**: JSON请求和响应格式
- **状态码**: 标准HTTP状态码
- **错误处理**: 统一错误响应格式

#### 6.1.2 API端点完整列表（严格对应15个CGI）

**配置管理API (15个端点)**
```
GET/POST /api/v1/config/center      # 对应0center.c
GET/POST /api/v1/config/gateway     # 对应0gateway.c
GET/POST /api/v1/config/recorder    # 对应0recorder.c
GET/POST /api/v1/config/mini        # 对应0mini.c
GET/POST /api/v1/config/sci         # 对应0sci.c
GET/POST /api/v1/config/sci3g       # 对应0sci3g.c
GET/POST /api/v1/config/sci4g       # 对应0sci4g.c
GET/POST /api/v1/config/switch      # 对应0switch.c
GET/POST /api/v1/config/switch3g    # 对应0switch3g.c
GET/POST /api/v1/config/switchpub   # 对应0switchpub.c
GET/POST /api/v1/config/scipub      # 对应0scipub.c
GET/POST /api/v1/config/ntp         # 对应0ntp.c
```

**系统管理API (5个端点，对应0system.c+0down.c)**
```
POST /api/v1/system/reboot          # 系统重启
POST /api/v1/system/reset           # 配置重置
POST /api/v1/system/upload          # 文件上传
GET  /api/v1/system/logs            # 日志查看
GET  /api/v1/system/signal          # 3G信号检测
```

**认证管理API (3个端点，对应0passwd.c)**
```
POST /api/v1/auth/login             # 用户登录
POST /api/v1/auth/logout            # 用户注销
POST /api/v1/auth/password          # 密码修改
```

#### 6.1.3 API路由表设计
```c
// API路由结构体
typedef struct {
    const char *path;                    // API路径
    const char *method;                  // HTTP方法
    api_handler_t handler;               // 处理函数
} api_route_t;

// 统一路由表（23个API端点）
static const api_route_t s_api_routes[] = {
    // 配置管理API (15个)
    {"/api/v1/config/center",    "GET",  handle_center_config},
    {"/api/v1/config/center",    "POST", handle_center_config},
    {"/api/v1/config/gateway",   "GET",  handle_gateway_config},
    {"/api/v1/config/gateway",   "POST", handle_gateway_config},
    // ... 其他配置API

    // 系统管理API (5个)
    {"/api/v1/system/reboot",    "POST", handle_system_reboot},
    {"/api/v1/system/reset",     "POST", handle_system_reset},
    {"/api/v1/system/upload",    "POST", handle_system_upload},
    {"/api/v1/system/logs",      "GET",  handle_system_logs},
    {"/api/v1/system/signal",    "GET",  handle_system_signal},

    // 认证管理API (3个)
    {"/api/v1/auth/login",       "POST", handle_auth_login},
    {"/api/v1/auth/logout",      "POST", handle_auth_logout},
    {"/api/v1/auth/password",    "POST", handle_auth_password},
};
```

### 6.2 请求响应格式规范

#### 6.2.1 统一请求格式
```json
// GET请求：无请求体
// POST请求：JSON格式
{
    "data": {
        // 配置数据，结构对应旧项目配置结构体
    }
}
```

#### 6.2.2 统一响应格式
```json
// 成功响应
{
    "code": 200,
    "message": "success",
    "data": {
        // 响应数据
    }
}

// 错误响应
{
    "code": 400,
    "message": "参数错误",
    "error": "详细错误信息"
}
```

#### 6.2.3 配置数据结构映射
配置数据结构必须与旧项目完全一致：

```c
// 示例：呼叫中心配置结构体（严格对应旧项目stCfgCenter）
typedef struct {
    uint32_t center_no;         // 呼叫中心编号（24位）
    uint32_t center_outssi;     // 统一编号（24位）
    uint32_t center_inssi;      // 统一应答号（24位）
    uint8_t  vchan_sum;         // 语音通道数量
    uint16_t center_voice_port; // 语音端口
    uint16_t listen_agent_port; // 监听端口
    uint8_t  peer_net_type;     // 网络类型
    uint32_t send_all_agent_ip; // 代理IP
    uint16_t send_to_agent_port;// 代理端口
    uint16_t inssi_num;         // 应答号个数
    uint16_t spec_function;     // 特殊功能标志
} __attribute__((packed)) stCfgCenter;
```

## 7. 技术选型

### 7.1 后端技术栈
- **HTTP服务器**: libmicrohttpd（轻量级、高性能）
- **JSON处理**: cJSON库（轻量级、C语言原生）
- **构建系统**: CMake（支持交叉编译）
- **第三方库**: 静态编译，减少依赖

### 7.2 前端技术栈
- **HTML**: HTML5（替代frameset）
- **CSS**: CSS3（响应式设计）
- **JavaScript**: 原生JavaScript（无框架依赖）
- **架构**: 单页应用（SPA）

## 8. 重构实施约束

### 8.1 功能约束
- **15个CGI程序对应15个API端点**：功能一一对应，不增加新接口
- **配置文件格式完全兼容**：二进制文件和INI文件格式不变
- **数据结构完全一致**：所有配置结构体与旧项目保持一致
- **业务逻辑不变**：保持旧项目的简单性，不增加复杂业务规则

### 8.2 技术约束
- **平台支持**：必须支持am335x、zynq、s3c2440三个ARM平台
- **第三方库**：静态编译，不修改源码
- **构建系统**：使用CMake替代多个重复的Makefile
- **代码复用**：最大化复用旧项目1rw*.c代码

### 8.3 质量约束
- **代码重复消除**：通过统一接口减少60%的重复代码
- **模块化设计**：便于维护，但功能边界不变
- **错误处理**：保持与旧项目一致的错误处理方式
- **性能要求**：不能低于旧项目的性能表现

## 9. 总结

### 9.1 重构核心成果
1. **架构现代化**：从CGI模式升级为HTTP服务模式
2. **前后端分离**：使用RESTful API替代CGI程序
3. **代码重复消除**：通过统一接口减少60%的重复代码
4. **构建系统升级**：使用CMake替代多个重复的Makefile
5. **界面现代化**：HTML5+CSS3替代frameset布局

### 9.2 严格约束遵循
- **功能一致性**：15个CGI程序对应15个API端点
- **配置兼容性**：配置文件格式和数据结构完全兼容
- **平台兼容性**：支持am335x、zynq、s3c2440三个ARM平台
- **复杂度控制**：新系统复杂度不超过旧系统
- **业务逻辑不变**：保持旧项目的简单性

### 9.3 技术优势
- **libmicrohttpd**：轻量级HTTP服务器，性能优异
- **cJSON**：轻量级JSON处理，内存占用小
- **CMake**：现代化构建系统，支持交叉编译
- **静态编译**：减少运行时依赖，提高稳定性

### 9.4 实施保障
- **分阶段实施**：按模块逐步重构，降低风险
- **功能验证**：每个模块重构后进行功能对比验证
- **配置兼容性测试**：确保配置文件完全兼容
- **多平台测试**：在三个ARM平台上进行测试验证

这个重构方案在严格遵循项目分析报告约束的前提下，实现了系统的现代化改造，为项目的长期维护和发展奠定了良好基础。
