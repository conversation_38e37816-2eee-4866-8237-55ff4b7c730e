# 重构方案设计文档

## 1. 重构目标与约束

### 1.1 重构核心目标
- **100%功能兼容**: 保持所有现有功能不变，**严禁增加任何新功能**
- **前后端分离**: 使用libmicrohttpd替代cgic，提供RESTful API接口
- **现代化界面**: 使用HTML5+CSS3替代frameset布局，保持相同功能
- **构建系统升级**: 使用cmake替代多个重复的Makefile
- **多平台支持**: 保持对am335x、zynq、s3c2440三个平台的支持
- **模块化设计**: 将重复代码提取为公共模块，**不改变功能边界**
- **代码重复消除**: 通过统一接口减少60%的重复代码

### 1.2 严格约束条件
- **功能约束**: 15个CGI程序对应15个API端点，功能一一对应
- **配置兼容性**: 配置文件格式、存储路径、数据结构完全兼容
- **业务逻辑约束**: 保持业务逻辑不变，**严禁增加复杂的业务规则**
- **界面约束**: 支持中英文界面切换，保持相同的功能布局
- **构建约束**: 第三方库静态编译，放置在3rdparty目录
- **复杂度约束**: **新系统复杂度不能超过旧系统**
- **平台约束**: 必须支持原有的三个ARM平台交叉编译

### 1.3 CGI程序与API端点严格对应

| 旧CGI程序 | 新API端点 | 代码行数 | 功能描述 | 严格约束 |
|-----------|-----------|----------|----------|----------|
| 0center.c | /api/v1/config/center | 419行 | 呼叫中心配置读写 | 保持所有配置结构体字段 |
| 0gateway.c | /api/v1/config/gateway | 410行 | 网关配置读写 | 与center共享相同结构体 |
| 0recorder.c | /api/v1/config/recorder | 中等 | 录音配置读写 | 包含对等基站配置 |
| 0mini.c | /api/v1/config/mini | 中等 | 迷你基站配置读写 | 与recorder类似结构 |
| 0sci.c | /api/v1/config/sci | 252行 | SCI基站配置读写 | 基站控制器配置 |
| 0sci3g.c | /api/v1/config/sci3g | 中等 | SCI基站3G配置读写 | 3G网络环境配置 |
| 0sci4g.c | /api/v1/config/sci4g | 中等 | SCI基站4G配置读写 | 4G网络环境配置 |
| 0switch.c | /api/v1/config/switch | 289行 | 交换机配置读写 | 交换机设备配置 |
| 0switch3g.c | /api/v1/config/switch3g | 中等 | 交换机3G配置读写 | 3G网络环境配置 |
| 0switchpub.c | /api/v1/config/switchpub | 中等 | 公用交换机配置读写 | 公共交换机配置 |
| 0scipub.c | /api/v1/config/scipub | 中等 | 公用SCI基站配置读写 | 公共SCI基站配置 |
| 0system.c | /api/v1/system/* | 102行 | 系统管理（重启、重置） | **极简实现**：仅3个操作 |
| 0ntp.c | /api/v1/config/ntp | 简单 | NTP时间同步配置 | 时间同步服务配置 |
| 0passwd.c | /api/v1/auth/password | 简单 | 密码管理 | 管理员密码修改 |
| 0down.c | /api/v1/system/logs,signal | 329行 | 日志查看和3G信号检测 | 串口AT命令检测 |

## 2. 项目目录结构设计

### 2.1 总体目录结构

```
webcfg_mod/
├── src/                    # C源代码
│   ├── main.c             # 入口点，HTTP服务器设置
│   ├── api/               # API路由系统
│   │   ├── api_router.c   # 统一路由管理
│   │   └── api_router.h   # 路由接口定义
│   ├── config/            # 配置模块（严格对应15个CGI程序）
│   │   ├── center_config.c      # 呼叫中心配置（对应0center.c）
│   │   ├── gateway_config.c     # 网关配置（对应0gateway.c）
│   │   ├── recorder_config.c    # 录音配置（对应0recorder.c）
│   │   ├── mini_config.c        # 迷你基站配置（对应0mini.c）
│   │   ├── sci_config.c         # SCI基站配置（对应0sci.c）
│   │   ├── sci3g_config.c       # SCI基站3G配置（对应0sci3g.c）
│   │   ├── sci4g_config.c       # SCI基站4G配置（对应0sci4g.c）
│   │   ├── switch_config.c      # 交换机配置（对应0switch.c）
│   │   ├── switch3g_config.c    # 交换机3G配置（对应0switch3g.c）
│   │   ├── switchpub_config.c   # 公用交换机配置（对应0switchpub.c）
│   │   ├── scipub_config.c      # 公用SCI基站配置（对应0scipub.c）
│   │   └── ntp_config.c         # NTP时间同步配置（对应0ntp.c）
│   ├── system/                  # 系统管理（对应0system.c+0down.c）
│   │   ├── system_management.c  # 系统管理：重启、重置、上传
│   │   └── system_logs.c        # 日志查看和信号检测
│   ├── auth/              # 认证模块（对应0passwd.c）
│   │   └── auth_handler.c # 密码管理
│   └── utils/             # 工具库（复用旧项目1rw*.c功能）
│       ├── config_utils.c       # 配置文件读写工具
│       ├── network_utils.c      # 网络工具函数
│       ├── file_utils.c         # 文件操作工具
│       └── json_utils.c         # JSON处理工具
├── web/                   # Web前端
│   ├── index.html         # 单页应用入口
│   ├── js/                # 模块化JavaScript
│   │   ├── api.js                 # HTTP客户端与认证
│   │   ├── router.js              # 单页路由
│   │   ├── auth-manager.js        # 认证状态管理
│   │   ├── ui-core.js             # 统一UI组件
│   │   └── pages/                 # 页面处理器（严格对应15个CGI）
│   │       ├── center-config.js        # 呼叫中心配置
│   │       ├── gateway-config.js       # 网关配置
│   │       ├── recorder-config.js      # 录音配置
│   │       ├── mini-config.js          # 迷你基站配置
│   │       ├── sci-config.js           # SCI基站配置
│   │       ├── sci3g-config.js         # SCI基站3G配置
│   │       ├── sci4g-config.js         # SCI基站4G配置
│   │       ├── switch-config.js        # 交换机配置
│   │       ├── switch3g-config.js      # 交换机3G配置
│   │       ├── switchpub-config.js     # 公用交换机配置
│   │       ├── scipub-config.js        # 公用SCI基站配置
│   │       ├── ntp-config.js           # NTP时间同步配置
│   │       ├── system-management.js    # 系统管理
│   │       ├── system-logs.js          # 日志查看和信号检测
│   │       └── auth-config.js          # 认证配置
│   └── css/               # 样式和响应式设计
│       ├── main.css       # 主样式文件
│       └── responsive.css # 响应式布局
├── 3rdparty/              # 第三方库（静态编译）
│   ├── libmicrohttpd/     # HTTP服务器库（替代cgic）
│   └── cjson/             # JSON解析库
├── cmake/                 # 构建配置和工具链
│   ├── toolchains/        # 交叉编译工具链配置
│   │   ├── am335x.cmake   # AM335x平台工具链
│   │   ├── zynq.cmake     # Zynq平台工具链
│   │   └── s3c2440.cmake  # S3C2440平台工具链
│   └── modules/           # CMake模块
├── scripts/               # 构建和部署脚本
│   ├── build.sh           # 统一构建脚本
│   └── deploy.sh          # 部署脚本
├── install/               # 安装配置
│   └── systemd/           # systemd服务配置
└── docs/                  # 文档
    ├── 01_项目分析报告.md
    ├── 02_重构设计方案.md
    └── 03_重构实施步骤.md
```

### 2.2 模块化设计原则

#### 2.2.1 配置模块统一接口
每个配置模块严格对应一个旧CGI程序，功能边界完全一致：

```c
// 统一配置模块接口
typedef struct {
    const char *module_name;           // 模块名称
    const char *config_file_path;      // 配置文件路径
    size_t config_struct_size;         // 配置结构大小
    int (*load_config)(void *config);  // 加载配置
    int (*save_config)(void *config);  // 保存配置
    int (*validate_config)(void *config); // 配置验证
} config_module_t;
```

#### 2.2.2 API处理统一模式
所有API处理函数遵循统一模式：

```c
// 统一API处理函数签名
typedef int (*api_handler_t)(struct MHD_Connection *connection,
                            const char *url,
                            const char *method,
                            const char *upload_data,
                            size_t *upload_data_size);

// 配置API处理函数命名规范
int handle_center_config(struct MHD_Connection *connection, ...);
int handle_gateway_config(struct MHD_Connection *connection, ...);
```

## 3. 命名规范

### 3.1 文件和目录命名规范

#### 3.1.1 目录命名
- **源码目录**：小写字母和下划线，如 `src/`, `config/`, `utils/`
- **模块目录**：按功能模块命名，如 `config/`, `system/`, `auth/`
- **第三方库目录**：使用原库名，如 `3rdparty/libmicrohttpd/`, `3rdparty/cjson/`

#### 3.1.2 文件命名
- **C源文件**：模块名_config.c，如 `center_config.c`, `gateway_config.c`
- **C头文件**：模块名_config.h，如 `center_config.h`, `gateway_config.h`
- **配置文件**：保持与旧项目文件名完全一致
- **脚本文件**：功能描述.sh，如 `build.sh`, `deploy.sh`

### 3.2 代码命名规范

#### 3.2.1 函数命名
- **API处理函数**：`handle_模块_config`，如 `handle_center_config()`, `handle_gateway_config()`
- **配置操作函数**：`模块_config_操作`，如 `center_config_load()`, `center_config_save()`
- **工具函数**：`工具类型_操作`，如 `config_utils_read()`, `network_utils_validate()`
- **系统函数**：`system_操作`，如 `system_reboot()`, `system_reset()`

#### 3.2.2 变量命名
- **全局变量**：`g_变量名`，如 `g_server_port`, `g_config_path`
- **静态变量**：`s_变量名`，如 `s_api_routes`, `s_config_modules`
- **结构体成员**：小写字母和下划线，如 `server_ip`, `listen_port`
- **宏定义**：全大写字母和下划线，如 `MAX_CONFIG_SIZE`, `DEFAULT_PORT`

#### 3.2.3 结构体和类型命名
- **配置结构体**：保持与旧项目完全一致，如 `stCfgCenter`, `stCfgCommon`
- **API结构体**：`api_功能_t`，如 `api_route_t`, `api_response_t`
- **工具结构体**：`utils_功能_t`，如 `file_utils_t`, `json_utils_t`

#### 3.2.4 API路径命名（严格对应15个CGI）
- **配置API**：`/api/v1/config/模块名`
  - `/api/v1/config/center` (对应0center.c)
  - `/api/v1/config/gateway` (对应0gateway.c)
  - `/api/v1/config/recorder` (对应0recorder.c)
  - `/api/v1/config/mini` (对应0mini.c)
  - `/api/v1/config/sci` (对应0sci.c)
  - `/api/v1/config/sci3g` (对应0sci3g.c)
  - `/api/v1/config/sci4g` (对应0sci4g.c)
  - `/api/v1/config/switch` (对应0switch.c)
  - `/api/v1/config/switch3g` (对应0switch3g.c)
  - `/api/v1/config/switchpub` (对应0switchpub.c)
  - `/api/v1/config/scipub` (对应0scipub.c)
  - `/api/v1/config/ntp` (对应0ntp.c)
- **系统API**：`/api/v1/system/操作` (对应0system.c+0down.c)
  - `/api/v1/system/reboot`
  - `/api/v1/system/reset`
  - `/api/v1/system/upload`
  - `/api/v1/system/logs`
  - `/api/v1/system/signal`
- **认证API**：`/api/v1/auth/操作` (对应0passwd.c)
  - `/api/v1/auth/login`
  - `/api/v1/auth/logout`
  - `/api/v1/auth/password`

## 4. 总体架构设计

### 4.1 架构概览

```
┌─────────────────────────────────────────────────────────────┐
│                    前端层 (Web Frontend)                     │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   HTML5 SPA     │ │   CSS3 Styles   │ │   JavaScript    │ │
│  │  (替代frameset)  │ │  (现代化布局)    │ │  (模块化设计)    │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                               │ HTTP/JSON RESTful API
                               ▼
┌─────────────────────────────────────────────────────────────┐
│                  HTTP服务层 (HTTP Service)                   │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │  libmicrohttpd  │ │   API Router    │ │  JSON Handler   │ │
│  │  (替代cgic)      │ │  (统一路由)      │ │  (数据处理)      │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                               │
                               ▼
┌─────────────────────────────────────────────────────────────┐
│                 配置管理层 (Config Management)               │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │ 15个配置模块     │ │  系统管理模块    │ │  认证管理模块    │ │
│  │ (对应15个CGI)    │ │ (对应0system.c)  │ │ (对应0passwd.c)  │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                               │
                               ▼
┌─────────────────────────────────────────────────────────────┐
│                 数据存储层 (Data Storage)                    │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │  Binary Config  │ │   INI Config    │ │  System Files   │ │
│  │  (二进制配置)    │ │  (INI配置)      │ │  (系统文件)      │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 4.2 分层设计详述

#### 4.2.1 前端层 (Web Frontend)
- **技术栈**: HTML5 + CSS3 + JavaScript (原生，无框架)
- **架构**: 单页应用 (SPA) 替代frameset布局
- **路由**: 基于hash的客户端路由，对应15个配置页面
- **交互**: 基于fetch API的异步通信，替代表单提交
- **样式**: 响应式设计，保持工业设备界面风格

#### 4.2.2 HTTP服务层 (HTTP Service)
- **技术栈**: C语言 + libmicrohttpd HTTP服务器
- **协议**: RESTful API + JSON数据格式
- **认证**: 基于session token的身份验证
- **路由**: 统一的API路由管理，15个配置API + 5个系统API + 3个认证API
- **功能约束**: **严格对应旧CGI程序功能，不增加新接口**

#### 4.2.3 配置管理层 (Config Management)
- **复用策略**: 最大化复用现有1rw*.c代码，**不增加新的业务规则**
- **重构范围**: 仅重构接口层，**保持核心逻辑完全不变**
- **模块化**: 15个配置模块对应15个CGI程序，**功能边界完全一致**
- **简单性**: **保持旧项目的简单性，不引入复杂的业务流程**

#### 4.2.4 数据存储层 (Data Storage)
- **兼容性**: 完全保持现有配置文件格式，**不增加新的配置项**
- **封装**: 提供统一的配置读写接口，**功能与旧CGI的文件操作一致**
- **事务性**: 保证配置操作的原子性，**但不增加复杂的事务管理**

## 5. 构建系统设计

### 5.1 混合构建系统架构

#### 5.1.1 主项目CMake配置
```cmake
cmake_minimum_required(VERSION 3.12)
project(webcfg_system)

# 平台检测和交叉编译配置
if(CMAKE_SYSTEM_PROCESSOR MATCHES "arm")
    if(DEFINED ENV{CROSS_COMPILE})
        set(CMAKE_C_COMPILER $ENV{CROSS_COMPILE}gcc)
        set(CMAKE_CXX_COMPILER $ENV{CROSS_COMPILE}g++)
    endif()
endif()

# 编译选项
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_FLAGS "-Wall -O2")
set(CMAKE_C_FLAGS_DEBUG "-O0 -g3 -DDEBUG")
set(CMAKE_C_FLAGS_RELEASE "-O3 -DNDEBUG")

# 查找预构建的第三方库
find_library(CJSON_LIB cjson PATHS ${WEBCFG_CACHE_DIR}/cjson/lib)
find_library(MICROHTTPD_LIB microhttpd PATHS ${WEBCFG_CACHE_DIR}/microhttpd/lib)
find_library(CGIC_LIB cgic PATHS ${WEBCFG_CACHE_DIR}/cgic/lib)

# 子目录
add_subdirectory(src)
add_subdirectory(web)
```

#### 5.1.2 第三方库管理策略
```cmake
# 简化的第三方库管理 (cmake/ThirdParty.cmake)
function(add_3rdparty_library name)
    set(LIB_DIR ${CMAKE_SOURCE_DIR}/3rdparty/${name})
    set(BUILD_DIR ${CMAKE_BINARY_DIR}/3rdparty/${name})

    if(EXISTS ${LIB_DIR}/CMakeLists.txt)
        # 使用CMake构建
        add_subdirectory(${LIB_DIR} ${BUILD_DIR})
    elseif(EXISTS ${LIB_DIR}/configure.ac)
        # 使用autotools构建
        include(cmake/AutotoolsBuild.cmake)
        build_with_autotools(${name} ${LIB_DIR} ${BUILD_DIR})
    else()
        # 使用Makefile构建
        include(cmake/MakefileBuild.cmake)
        build_with_makefile(${name} ${LIB_DIR} ${BUILD_DIR})
    endif()
endfunction()

# 添加所需的第三方库
add_3rdparty_library(cjson)
add_3rdparty_library(microhttpd)
add_3rdparty_library(cgic)
```

### 5.2 跨平台构建支持

#### 5.2.1 支持的平台
- **native**: 本地开发 (x86_64 Linux)
- **am335x**: TI AM335x ARM Cortex-A8
- **zynq**: Xilinx Zynq ARM Cortex-A9
- **2440**: Samsung S3C2440 ARM920T
- **ec20**: Quectel EC20 LTE模块

#### 5.2.2 交叉编译配置
```cmake
# 统一的交叉编译配置函数
function(setup_cross_compile platform)
    if(platform STREQUAL "am335x")
        set(CMAKE_C_COMPILER /opt/platform/am335x/sdk/bin/arm-linux-gnueabihf-gcc)
        set(CMAKE_SYSROOT /opt/platform/am335x/sdk/arm-none-linux-gnueabi/sys-root)
    elseif(platform STREQUAL "zynq")
        set(CMAKE_C_COMPILER /opt/platform/zynq/sdk/bin/arm-linux-gnueabihf-gcc)
        set(CMAKE_SYSROOT /opt/platform/zynq/sdk/arm-none-linux-gnueabi/sys-root)
    elseif(platform STREQUAL "2440")
        set(CMAKE_C_COMPILER /opt/platform/2440/sdk/bin/arm-linux-gcc)
        set(CMAKE_SYSROOT /opt/platform/2440/sdk/arm-linux/sys-root)
    elseif(platform STREQUAL "ec20")
        set(CMAKE_C_COMPILER /opt/platform/ec20/sdk/bin/arm-linux-gnueabihf-gcc)
        set(CMAKE_SYSROOT /opt/platform/ec20/sdk/arm-none-linux-gnueabi/sys-root)
    endif()

    # 统一的编译选项
    set(CMAKE_C_FLAGS "-Wall -O2 -g")
    set(CMAKE_C_FLAGS_RELEASE "-O3 -DNDEBUG")
    set(CMAKE_C_FLAGS_DEBUG "-O0 -g3 -DDEBUG")
endfunction()
```

### 5.3 构建命令和脚本

#### 5.3.1 快速构建
```bash
# 默认构建（native平台）
./scripts/build/build.sh

# 指定平台构建
./scripts/build/build.sh --platform=am335x --type=Release

# 清理并重新构建
./scripts/build/build.sh --clean --rebuild-deps
```

#### 5.3.2 手动构建
```bash
mkdir -p build && cd build
cmake -DTARGET_PLATFORM=native -DCMAKE_BUILD_TYPE=Debug ..
make -j$(nproc)
```

## 6. 技术选型方案

### 6.1 后端技术选型

#### 6.1.1 HTTP服务器
**选择方案**: libmicrohttpd
- 轻量级、高性能
- 支持多线程
- API简洁易用
- 使用autotools原生构建系统
- 静态库编译，减少依赖

#### 6.1.2 JSON处理
**选择方案**: cJSON库
- 轻量级JSON解析库
- C语言原生支持
- API简单直观
- 内存占用小
- 使用cmake原生构建系统
- 静态库编译，减少依赖

#### 6.1.3 简化的API框架设计
```c
// 简化的API路由设计
typedef struct {
    char *path;                    // API路径
    int (*get_handler)(cJSON **);  // GET处理函数
    int (*post_handler)(cJSON *);  // POST处理函数
} simple_api_route_t;

// 简化的路由表
static simple_api_route_t s_api_routes[] = {
    // 板卡配置（对应旧项目板卡功能）
    {"/api/v1/config/board/basic",    handle_board_basic_get,     handle_board_basic_post},
    {"/api/v1/config/board/ethernet", handle_board_ethernet_get,  handle_board_ethernet_post},
    {"/api/v1/config/board/ntp",      handle_board_ntp_get,       handle_board_ntp_post},   // 对应0ntp.c
    
    // 设备配置
    {"/api/v1/config/center",   handle_center_get,   handle_center_post},      // 对应0center.c
    {"/api/v1/config/gateway",  handle_gateway_get,  handle_gateway_post},     // 对应0gateway.c
    {"/api/v1/config/recorder", handle_recorder_get, handle_recorder_post},    // 对应0recorder.c+0mini.c
    {"/api/v1/config/sci",      handle_sci_get,      handle_sci_post},         // 对应0sci*.c
    {"/api/v1/config/switch",   handle_switch_get,   handle_switch_post},      // 对应0switch*.c
    
    // 与设备配置模块组合配置
    {"/api/v1/config/spec/boardnum",     handle_spec_boardnum_get,     handle_spec_boardnum_post},
    {"/api/v1/config/spec/vocoder",      handle_spec_vocoder_get,      handle_spec_vocoder_post},
    {"/api/v1/config/spec/syscode",      handle_spec_syscode_get,      handle_spec_syscode_post},
    {"/api/v1/config/spec/netselection", handle_spec_netselection_get, handle_spec_netselection_post},
    {"/api/v1/config/spec/wlan",         handle_spec_wlan_get,         handle_spec_wlan_post},
    {"/api/v1/config/spec/wwan",         handle_spec_wwan_get,         handle_spec_wwan_post},
    
    // 系统管理（对应0system.c+0down.c，功能极简）
    {"/api/v1/system/upload",   NULL,                handle_system_upload},    // 文件上传
    {"/api/v1/system/reboot",   NULL,                handle_system_reboot},    // 重启设备
    {"/api/v1/system/reset",    NULL,                handle_system_reset},     // 配置重置
    {"/api/v1/system/logs",     handle_system_logs,  NULL},                    // 日志查看
    {"/api/v1/system/signal",   handle_system_signal, NULL},                   // 信号检测
    
    // 认证管理（对应0passwd.c，功能极简）
    {"/api/v1/auth/password",   NULL,                handle_auth_password},    // 密码修改
    // 新增 基本登录登出认证
    {"/api/v1/auth/login",      NULL,                handle_auth_login},       // 用户登录
    {"/api/v1/auth/logout",     NULL,                handle_auth_logout},      // 用户注销
};
```

### 6.2 前端技术选型

#### 6.2.1 单页应用架构
**HTML5结构设计**:
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VICTEL IP交换机配置系统</title>
    <link rel="stylesheet" href="css/main.css">
</head>
<body>
    <div class="app-container">
        <header class="app-header">
            <h1>VICTEL IP交换机配置系统</h1>
            <nav class="main-nav">
                <!-- 导航菜单 -->
            </nav>
        </header>
        <main class="app-main">
            <aside class="app-sidebar">
                <!-- 侧边栏菜单 -->
            </aside>
            <section class="app-content">
                <!-- 主要内容区域 -->
            </section>
        </main>
    </div>
    <script src="js/api.js"></script>
    <script src="js/router.js"></script>
    <script src="js/auth-manager.js"></script>
    <script src="js/ui-core-unified.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
```

#### 6.2.2 模块化JavaScript架构
```javascript
// 主应用模块
const App = {
    config: {},
    api: {},
    ui: {},
    utils: {},
    auth: {},
    router: {},
    init: function() {
        // 应用初始化
        this.auth.checkLoginState();
        this.router.init();
        this.ui.init();
    }
};

// 认证模块
const Auth = {
    isLoggedIn: function() {
        return !!sessionStorage.getItem('authToken');
    },
    login: function(token) {
        sessionStorage.setItem('authToken', token);
    },
    logout: function() {
        sessionStorage.removeItem('authToken');
    },
    getToken: function() {
        return sessionStorage.getItem('authToken');
    }
};

// API通信模块
const API = {
    baseURL: '/api/v1',
    request: async function(method, url, data) {
        const headers = {
            'Content-Type': 'application/json'
        };
        if (Auth.isLoggedIn()) {
            headers['Authorization'] = `Bearer ${Auth.getToken()}`;
        }
        
        const response = await fetch(this.baseURL + url, {
            method: method,
            headers: headers,
            body: data ? JSON.stringify(data) : null
        });
        
        return await response.json();
    },
    
    // 配置API（严格对应旧CGI功能）
    config: {
        board: {
            basic: {
                get: () => API.request('GET', '/config/board/basic'),
                save: (data) => API.request('POST', '/config/board/basic', data)
            },
            ethernet: {
                get: () => API.request('GET', '/config/board/ethernet'),
                save: (data) => API.request('POST', '/config/board/ethernet', data)
            },
            ntp: {
                get: () => API.request('GET', '/config/board/ntp'),
                save: (data) => API.request('POST', '/config/board/ntp', data)
            }
        },
        center: {
            get: () => API.request('GET', '/config/center'),
            save: (data) => API.request('POST', '/config/center', data)
        },
        gateway: {
            get: () => API.request('GET', '/config/gateway'),
            save: (data) => API.request('POST', '/config/gateway', data)
        },
        recorder: {
            get: () => API.request('GET', '/config/recorder'),
            save: (data) => API.request('POST', '/config/recorder', data)
        },
        sci: {
            get: () => API.request('GET', '/config/sci'),
            save: (data) => API.request('POST', '/config/sci', data)
        },
        switch: {
            get: () => API.request('GET', '/config/switch'),
            save: (data) => API.request('POST', '/config/switch', data)
        },
        spec: {
            selection: {
                get: () => API.request('GET', '/config/spec/netselection'),
                save: (data) => API.request('POST', '/config/spec/netselection', data)
            },
            common: {
                get: () => API.request('GET', '/config/spec/boardnum'),
                save: (data) => API.request('POST', '/config/spec/boardnum', data)
            },
            wlan: {
                get: () => API.request('GET', '/config/spec/wlan'),
                save: (data) => API.request('POST', '/config/spec/wlan', data)
            },
            wwan: {
                get: () => API.request('GET', '/config/spec/wwan'),
                save: (data) => API.request('POST', '/config/spec/wwan', data)
            },
            vocoder: {
                get: () => API.request('GET', '/config/spec/vocoder'),
                save: (data) => API.request('POST', '/config/spec/vocoder', data)
            },
            syscode: {
                get: () => API.request('GET', '/config/spec/syscode'),
                save: (data) => API.request('POST', '/config/spec/syscode', data)
            }
        }
    },
    
    // 系统API（对应0system.c+0down.c）
    system: {
        reboot: () => API.request('POST', '/system/reboot'),
        reset: () => API.request('POST', '/system/reset'),
        logs: () => API.request('GET', '/system/logs'),
        signal: () => API.request('GET', '/system/signal'),
        upload: (file) => API.request('POST', '/system/upload', file)
    },
    
    // 认证API（对应0passwd.c）
    auth: {
        login: (credentials) => API.request('POST', '/auth/login', credentials),
        logout: () => API.request('POST', '/auth/logout'),
        password: (passwordData) => API.request('POST', '/auth/password', passwordData)
    }
};
```

## 7. API接口设计（严格对应旧CGI功能）

### 7.1 RESTful API规范

#### 7.1.1 URL设计规范（与旧CGI一一对应）
```
基础URL: http://device-ip/api/v1

板卡配置相关（对应旧项目基本配置、以太网配置、ntp配置）：
GET  /api/v1/config/board/basic     - 板卡基本配置
POST /api/v1/config/board/basic
GET  /api/v1/config/board/ethernet  - 以太网配置
POST /api/v1/config/board/ethernet
GET  /api/v1/config/board/ntp       - 对应0ntp.c读取功能
POST /api/v1/config/board/ntp       - 对应0ntp.c保存功能

设备配置相关:
GET  /api/v1/config/center            - 对应0center.c读取功能
POST /api/v1/config/center            - 对应0center.c保存功能
GET  /api/v1/config/gateway           - 对应0gateway.c读取功能
POST /api/v1/config/gateway           - 对应0gateway.c保存功能
GET  /api/v1/config/sci               - 对应0sci*.c读取功能
POST /api/v1/config/sci               - 对应0sci*.c保存功能
GET  /api/v1/config/switch            - 对应0switch*.c读取功能
POST /api/v1/config/switch            - 对应0switch*.c保存功能
GET  /api/v1/config/recorder          - 对应0recorder.c/0mini.c读取功能
POST /api/v1/config/recorder          - 对应0recorder.c/0mini.c保存功能

与设备配置模块组合配置：
GET  /api/v1/config/spec/boardnum     - 模块号码配置
POST /api/v1/config/spec/boardnum
GET  /api/v1/config/spec/vocoder      - 编解码配置
POST /api/v1/config/spec/vocoder
GET  /api/v1/config/spec/syscode      - 系统码配置
POST /api/v1/config/spec/syscode
GET  /api/v1/config/spec/netselection - 网络选择配置
POST /api/v1/config/spec/netselection
GET  /api/v1/config/spec/wlan         - 无线网络配置
POST /api/v1/config/spec/wlan
GET  /api/v1/config/spec/wwan         - 3G网络配置
POST /api/v1/config/spec/wwan

系统相关（对应旧CGI系统功能，功能极简）:
POST /api/v1/system/reboot            - 对应0system.c重启功能
POST /api/v1/system/reset             - 对应0system.c重置配置功能
POST /api/v1/system/upload            - 对应0system.c文件上传功能
GET  /api/v1/system/logs              - 对应0down.c日志显示功能
GET  /api/v1/system/signal            - 对应0down.c 3G信号检测功能

认证相关（对应0passwd.c，功能极简）:
POST /api/v1/auth/login               - 用户登录接口，返回认证token
POST /api/v1/auth/logout              - 用户注销接口，清除会话token
POST /api/v1/auth/password            - 对应0passwd.c密码修改功能
```

**重要说明**: 每个API接口都严格对应一个旧CGI程序的功能，不增加额外的接口或功能。

#### 7.1.2 请求响应格式
```json
// 统一请求格式
{
    "action": "save",
    "data": {
        // 具体数据
    }
}

// 统一响应格式
{
    "code": 200,
    "message": "success",
    "data": {
        // 响应数据
    },
    "timestamp": 1635123456
}

// 错误响应格式
{
    "code": 400,
    "message": "参数错误",
    "error": "Invalid IP address format",
    "timestamp": 1635123456
}
```

### 7.2 核心API设计

#### 7.2.1 板卡通用配置API
```c
// 网络配置结构体（严格对应旧项目stCfgNet）
typedef struct {
    uint32_t ip;        // IP地址
    uint32_t mask;      // 子网掩码
    uint32_t gateway;   // 网关
    uint32_t dns;       // DNS服务器
    uint8_t mac[6];     // MAC地址
} cfg_network_t;

// API处理函数
int handle_board_ethernet_get(MHD_Connection *connection, const char *url, cJSON *request_data);
int handle_board_ethernet_post(MHD_Connection *connection, const char *url, cJSON *request_data);
```

#### 7.2.2 设备配置API
```c
// 呼叫中心配置结构体（严格对应旧项目stCfgCenter）
typedef struct {
    uint32_t center_no;         // 呼叫中心编号（24位）
    uint32_t center_outssi;     // 统一编号（24位）
    uint32_t center_inssi;      // 统一应答号（24位）
    uint16_t vchan_sum;         // 语音通道数量
    uint16_t center_voice_port; // 语音端口
    uint16_t listen_agent_port; // 监听端口
    uint8_t peer_net_type;      // 网络类型
    uint32_t send_all_agent_ip; // 代理IP
    uint16_t send_to_agent_port; // 代理端口
    uint8_t inssi_num;          // 应答号个数
    uint8_t spec_function;      // 特殊功能标志
} cfg_center_t;

// API处理函数
int handle_center_get(MHD_Connection *connection, const char *url, cJSON *request_data);
int handle_center_post(MHD_Connection *connection, const char *url, cJSON *request_data);
```

## 8. 开发工作流程

### 8.1 开发环境设置

#### 8.1.1 环境准备
```bash
# 安装依赖
sudo apt-get install build-essential cmake curl jq

# 克隆项目
git clone <repository-url>
cd webcfg_mod

# 构建开发版本
./scripts/build/build.sh --type=Debug --verbose
```

#### 8.1.2 开发服务器启动
```bash
# 启动开发服务器
./build/webcfg-server --port=8080 --verbose --web-root=./web

# 使用自定义配置
./build/webcfg-server --config=./webcfg.conf
```

### 8.2 开发流程

#### 8.2.1 添加新配置模块
1. 在`src/config/`目录创建模块文件
2. 实现标准化的处理函数
3. 在`main.c`中注册API路由
4. 创建对应的前端页面
5. 更新导航菜单

#### 8.2.2 添加新API端点
```c
// 1. 添加处理函数
int handle_custom_endpoint(MHD_Connection *connection, const char *url, cJSON *request_data);

// 2. 注册路由
api_router_register("/api/v1/custom", HTTP_METHOD_GET, handle_custom_endpoint);

// 3. 添加前端集成
// 更新web/js/api.js和相关页面文件
```

### 8.3 测试流程

#### 8.3.1 运行测试
```bash
# 运行所有测试
./scripts/tests/test.sh

# 运行特定测试
./scripts/tests/test.sh api --host=localhost --port=8080

# 生成测试报告
./scripts/tests/test.sh --report
```

#### 8.3.2 手动测试
```bash
# 测试API端点
curl -X GET http://localhost:8080/api/v1/config/center
curl -X POST http://localhost:8080/api/v1/config/center -d '{"data": {...}}'
```

## 9. 数据库设计

### 9.1 配置存储策略
- **保持兼容**: 继续使用现有二进制和ini配置文件格式
- **添加缓存**: 内存中维护配置数据缓存
- **事务支持**: 实现配置修改的回滚机制

### 9.2 配置映射设计
```c
// 配置映射表
typedef struct {
    char *api_path;           // API路径
    char *config_file;        // 配置文件路径
    size_t config_size;       // 配置大小
    int (*validator)(void *); // 数据验证函数
    int (*converter)(cJSON *, void *); // JSON转换函数
} config_mapping_t;

// 配置映射表实例（严格对应旧CGI）
static config_mapping_t config_mappings[] = {
    {"/api/v1/config/board/ethernet", "/etc/eth0-setting", sizeof(stCfgNet), 
     validate_network, convert_network_json},
    {"/api/v1/config/center", "/home/<USER>/cfg/callcenter.cfg", sizeof(stCfgCenter), 
     validate_center, convert_center_json},
    {"/api/v1/config/gateway", "/home/<USER>/cfg/gateway.cfg", sizeof(stCfgCenter), 
     validate_gateway, convert_gateway_json},
    {"/api/v1/config/recorder", "/home/<USER>/cfg/record.cfg", sizeof(stCfgConf), 
     validate_recorder, convert_recorder_json},
    // ... 其他模块配置映射
};
```

## 10. 测试策略

### 10.1 测试类型

#### 10.1.1 单元测试
- **配置模块测试**: 测试配置读写功能
- **API处理测试**: 测试API端点处理
- **数据验证测试**: 测试输入数据验证
- **工具函数测试**: 测试工具函数正确性

#### 10.1.2 集成测试
- **前后端集成**: 测试API与前端的集成
- **多平台编译**: 测试跨平台编译
- **配置文件兼容性**: 测试与旧配置文件的兼容性
- **第三方库集成**: 测试第三方库的集成

#### 10.1.3 性能测试
- **负载测试**: 使用wrk/ab进行负载测试
- **并发测试**: 测试多用户并发访问
- **内存测试**: 测试内存使用情况
- **响应时间测试**: 测试API响应时间

#### 10.1.4 安全测试
- **认证测试**: 测试用户认证机制
- **权限测试**: 测试访问控制
- **输入验证测试**: 测试恶意输入防护
- **会话管理测试**: 测试会话安全

### 10.2 测试命令

#### 10.2.1 自动化测试
```bash
# 运行单元测试
./scripts/tests/test.sh unit

# 运行API测试
./scripts/tests/test.sh api --host=localhost:8080

# 运行性能测试
./scripts/tests/test.sh performance --duration=60s

# 运行安全测试
./scripts/tests/test.sh security
```

#### 10.2.2 手动测试
```bash
# 测试特定端点
curl -X GET http://localhost:8080/api/v1/config/center
curl -X POST http://localhost:8080/api/v1/config/center -H "Content-Type: application/json" -d '{"data": {...}}'

# 测试认证
curl -X POST http://localhost:8080/api/v1/auth/login -d '{"username": "admin", "password": "password"}'
```

## 11. 安全设计

### 11.1 认证机制
```c
// Token结构设计
typedef struct {
    char token[64];      // 认证token
    time_t expires;      // 过期时间
    char username[32];   // 用户名
    int privileges;      // 权限级别
} auth_token_t;

// 认证中间件
int auth_middleware(MHD_Connection *connection, const char *url, cJSON *request_data);
```

### 11.2 输入验证
```c
// 输入验证函数
int validate_ip_address(const char *ip);
int validate_mac_address(const char *mac);
int validate_port_number(int port);
int validate_config_data(const char *config_type, cJSON *data);
```

### 11.3 会话管理
- 基于token的会话管理
- 会话超时控制
- 安全的会话存储

## 12. 部署方案

### 12.1 生产部署

#### 12.1.1 部署脚本
```bash
# 构建发布版本
./scripts/build/build.sh --type=Release --package

# 部署到生产环境
sudo ./scripts/deploy/deploy.sh --port=80 --force install

# 验证部署
./scripts/deploy/deploy.sh status
```

#### 12.1.2 服务管理
```bash
# systemd服务命令
sudo systemctl start webcfg
sudo systemctl stop webcfg
sudo systemctl restart webcfg
sudo systemctl status webcfg
sudo journalctl -u webcfg -f
```

### 12.2 配置管理

#### 12.2.1 主配置文件
- **主配置**: `/etc/webcfg/webcfg.conf`
- **模块配置**: `/home/<USER>/cfg/`目录下的各个配置文件
- **网络配置**: `/etc/`目录下的网络配置文件

#### 12.2.2 环境变量
```bash
# 构建配置
export WEBCFG_BUILD_ROOT=/tmp/webcfg-build
export WEBCFG_CACHE_ROOT=/tmp/webcfg-cache
export WEBCFG_INSTALL_ROOT=/usr/local

# 运行时配置
export WEBCFG_CONFIG_FILE=/etc/webcfg/webcfg.conf
export WEBCFG_WEB_ROOT=/var/www/webcfg
export WEBCFG_LOG_LEVEL=INFO
```

## 13. 性能优化

### 13.1 前端优化
- **资源压缩**: CSS/JS文件压缩
- **缓存策略**: 合理的缓存头设置
- **按需加载**: 模块化加载
- **响应式设计**: 适应不同设备

### 13.2 后端优化
- **内存管理**: 优化内存使用
- **并发处理**: 支持多线程处理
- **缓存机制**: 配置数据缓存
- **性能监控**: 性能指标监控

## 14. 兼容性保证

### 14.1 配置文件兼容
- **二进制格式**: 完全保持结构体内存布局
- **INI格式**: 保持键值对格式
- **文件路径**: 与旧项目路径完全一致
- **数据类型**: 保持数据类型一致

### 14.2 功能行为兼容
- **系统重启**: 对应旧项目重启逻辑
- **配置重置**: 对应旧项目重置逻辑
- **日志查看**: 对应旧项目日志功能
- **信号检测**: 对应旧项目信号检测
- **密码修改**: 对应旧项目密码管理

### 14.3 API行为兼容
- **数据格式**: JSON格式对应原始数据结构
- **验证规则**: 保持原有验证逻辑
- **错误处理**: 保持原有错误处理方式
- **业务流程**: 保持原有业务流程不变

## 15. 调试和故障排除

### 15.1 服务器调试
```bash
# 详细模式启动
./build/webcfg-server --verbose --port=8080

# 守护进程模式调试
sudo ./scripts/deploy/deploy.sh start
sudo journalctl -u webcfg -f
```

### 15.2 前端调试
- **浏览器开发工具**: JavaScript调试
- **网络选项卡**: API请求/响应检查
- **控制台日志**: 页面级调试

### 15.3 常见问题
- **端口冲突**: 检查端口占用情况
- **权限问题**: 检查文件权限设置
- **配置错误**: 检查配置文件格式
- **网络问题**: 检查网络连接

## 16. 总结

### 16.1 重构成果
- **100%功能兼容**: 所有功能严格对应旧CGI程序
- **架构现代化**: 前后端分离，RESTful API
- **代码质量提升**: 模块化设计，统一编码规范
- **构建系统升级**: CMake混合构建系统
- **多平台支持**: 保持所有平台的兼容性

### 16.2 技术亮点
1. **严格对应**: 每个新模块都严格对应旧CGI程序
2. **结构体兼容**: 所有配置结构体与旧项目完全一致
3. **配置升级**: 支持旧配置文件的自动读取
4. **模块化设计**: 统一的配置管理框架和API处理模式
5. **混合构建**: 主项目CMake + 第三方库原生构建

### 16.3 约束遵循
- **功能约束**: 严格不增加新功能
- **复杂度约束**: 保持系统简单性
- **兼容性约束**: 100%配置文件兼容
- **逻辑约束**: 保持业务逻辑不变

这个重构方案在严格遵循项目分析报告约束的前提下，实现了系统的现代化改造，为项目的长期维护和发展奠定了良好基础。 
