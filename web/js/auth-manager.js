/**
 * 简化的认证管理器
 * 专注于核心登录/登出功能，移除冗余的token管理逻辑
 */
class AuthManager {
    constructor() {
        this.tokenKey = 'auth_token';
        this.userKey = 'user_info';
    }

    /**
     * 用户登录
     * @param {string} username 
     * @param {string} password 
     * @returns {Promise<boolean>} 是否登录成功
     */
    async login(username, password) {
        try {
            console.log('开始登录:', username);
            
            const response = await API.post('/auth/login', {
                username,
                password
            });

            console.log('登录API响应:', response);

            if (response.code === 200) {
                // 登录成功
                this.setToken(response.data.token);
                this.setUser({ username: username });
                console.log('登录成功，token已保存:', response.data.token);
                return true;
            } else {
                // 登录失败
                this.showError(response.message || '登录失败');
                console.log('登录失败:', response.message);
                return false;
            }
        } catch (error) {
            console.error('登录请求失败:', error);
            this.showError('网络错误，请稍后重试');
            return false;
        }
    }

    /**
     * 用户登出
     */
    async logout() {
        try {
            await API.post('/auth/logout');
        } catch (error) {
            console.warn('登出API请求失败:', error);
        } finally {
            this.clearAuth();
            window.location.href = '/login.html';
        }
    }

    /**
     * 检查登录状态
     * @returns {boolean} 是否已登录
     */
    isLoggedIn() {
        const token = this.getToken();
        const isLoggedIn = !!token;
        console.log('检查登录状态:', isLoggedIn, 'Token:', token);
        return isLoggedIn;
    }

    /**
     * 获取当前token
     * @returns {string|null} token或null
     */
    getToken() {
        return sessionStorage.getItem(this.tokenKey);
    }

    /**
     * 设置token
     * @param {string} token 
     */
    setToken(token) {
        sessionStorage.setItem(this.tokenKey, token);
        console.log('Token已设置:', token);
    }

    /**
     * 设置用户信息
     * @param {object} user 
     */
    setUser(user) {
        sessionStorage.setItem(this.userKey, JSON.stringify(user));
        console.log('用户信息已设置:', user);
    }

    /**
     * 获取用户信息
     * @returns {object|null} 用户信息或null
     */
    getUser() {
        const user = sessionStorage.getItem(this.userKey);
        return user ? JSON.parse(user) : null;
    }

    /**
     * 清除认证信息
     */
    clearAuth() {
        sessionStorage.removeItem(this.tokenKey);
        sessionStorage.removeItem(this.userKey);
        console.log('认证信息已清除');
    }

    /**
     * 显示错误信息
     * @param {string} message 
     */
    showError(message) {
        const errorEl = document.getElementById('errorMessage');
        if (errorEl) {
            errorEl.textContent = message;
            errorEl.style.display = 'block';
        }
    }
}

// 创建全局auth实例
const auth = new AuthManager();

// 登录表单提交处理
document.addEventListener('DOMContentLoaded', () => {
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            console.log('提交登录表单:', username);
            
            const success = await auth.login(username, password);
            if (success) {
                console.log('登录成功，准备跳转到主页');
                window.location.href = '/index.html';
            }
        });
    }
});

// 导出到全局作用域
window.auth = auth; 