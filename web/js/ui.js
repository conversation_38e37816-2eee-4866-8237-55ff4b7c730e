/**
 * UI管理模块 - 统一入口
 * 负责协调各个UI子模块，保持向后兼容性
 * 已拆分为多个模块：ui-core.js, ui-navigation.js, ui-modal.js, ui-notification.js, ui-page.js
 */

const UI = {
    /**
     * 初始化UI管理器
     */
    init: function() {
        // 初始化核心模块
        if (window.UICore) {
            UICore.init();
        } else {
            console.error('UICore模块未加载');
        }
    },

    // 向后兼容性方法 - 委托给相应的子模块

    /**
     * 获取缓存的DOM元素（向后兼容）
     */
    get elements() {
        return window.UICore ? UICore.getAllElements() : {};
    },

    /**
     * 获取当前页面（向后兼容）
     */
    get currentPage() {
        return window.UICore ? UICore.currentPage : 'network-selection';
    },

    /**
     * 设置当前页面（向后兼容）
     */
    set currentPage(page) {
        if (window.UICore) {
            UICore.currentPage = page;
        }
    },

    // 核心功能方法（向后兼容）
    showLoading: function() {
        if (window.UICore) UICore.showLoading();
    },

    hideLoading: function() {
        if (window.UICore) UICore.hideLoading();
    },

    setStatus: function(text) {
        if (window.UICore) UICore.setStatus(text);
    },

    setConnectionStatus: function(connected) {
        if (window.UICore) UICore.setConnectionStatus(connected);
    },

    // 导航功能方法（向后兼容）
    navigateToPage: function(page) {
        if (window.UINavigation) UINavigation.navigateToPage(page);
    },

    setActiveNavLink: function(activeLink) {
        if (window.UINavigation) UINavigation.setActiveNavLink(activeLink);
    },

    updateBreadcrumb: function(page) {
        if (window.UINavigation) UINavigation.updateBreadcrumb(page);
    },

    // 模态框功能方法（向后兼容）
    showModal: function(title, content, onConfirm) {
        if (window.UIModal) UIModal.show(title, content, onConfirm);
    },

    hideModal: function() {
        if (window.UIModal) UIModal.hide();
    },

    confirmReboot: function() {
        if (window.UIModal) UIModal.confirmReboot();
    },

    confirmReset: function() {
        if (window.UIModal) UIModal.confirmReset();
    },

    confirmLogout: function() {
        if (window.UINavigation) UINavigation.confirmLogout();
    },

    // 通知功能方法（向后兼容）
    showNotification: function(message, type, duration) {
        if (window.UINotification) UINotification.show(message, type, duration);
    },

    hideNotification: function() {
        if (window.UINotification) UINotification.hide();
    },

    // 页面功能方法（向后兼容）
    loadPageContent: function(page) {
        if (window.UIPage) UIPage.loadPageContent(page);
    },

    renderPageContent: function(page) {
        if (window.UIPage) UIPage.renderPageContent(page);
    },

    bindPageEvents: function(page) {
        if (window.UIPage) UIPage.bindPageEvents(page);
    },

    handleFormSubmit: function(form, page) {
        if (window.UIPage) UIPage.handleFormSubmit(form, page);
    },

    handleButtonAction: function(action, page) {
        if (window.UIPage) UIPage.handleButtonAction(action, page);
    },

    savePageConfig: function(page, data) {
        if (window.UIPage) return UIPage.savePageConfig(page, data);
        return Promise.reject(new Error('UIPage模块未加载'));
    },

    refreshPageData: function(page) {
        if (window.UIPage) UIPage.refreshPageData(page);
    },

    // 页面渲染方法（向后兼容）
    renderBoardEthernetPage: function() {
        if (window.UIPage) return UIPage.renderBoardEthernetPage();
        return '<div class="card"><div class="card-header">以太网配置</div><div class="card-body">页面加载中...</div></div>';
    },

    renderDeviceConfigPage: function() {
        if (window.UIPage) return UIPage.renderDeviceConfigPage();
        return '<div class="card"><div class="card-header">设备配置</div><div class="card-body">页面加载中...</div></div>';
    },

    renderSystemPage: function() {
        if (window.UIPage) return UIPage.renderSystemPage();
        return '<div class="card"><div class="card-header">系统管理</div><div class="card-body">页面加载中...</div></div>';
    },

    renderCenterConfigPage: function() {
        if (window.UIPage) return UIPage.renderCenterConfigPage();
        return '<div class="card"><div class="card-header">呼叫中心配置</div><div class="card-body">页面加载中...</div></div>';
    },

    renderGatewayConfigPage: function() {
        if (window.UIPage) return UIPage.renderGatewayConfigPage();
        return '<div class="card"><div class="card-header">互联网关配置</div><div class="card-body">页面加载中...</div></div>';
    },

    renderRecorderConfigPage: function() {
        if (window.UIPage) return UIPage.renderRecorderConfigPage();
        return '<div class="card"><div class="card-header">录音配置</div><div class="card-body">页面加载中...</div></div>';
    },

    renderSCIConfigPage: function() {
        if (window.UIPage) return UIPage.renderSCIConfigPage();
        return '<div class="card"><div class="card-header">SCI基站配置</div><div class="card-body">页面加载中...</div></div>';
    },

    renderSwitchConfigPage: function() {
        if (window.UIPage) return UIPage.renderSwitchConfigPage();
        return '<div class="card"><div class="card-header">交换机配置</div><div class="card-body">页面加载中...</div></div>';
    },

    renderSystemLogsPage: function() {
        if (window.UIPage) return UIPage.renderSystemLogsPage();
        return '<div class="card"><div class="card-header">系统日志</div><div class="card-body">页面加载中...</div></div>';
    },

    renderSystemSignalPage: function() {
        if (window.UIPage) return UIPage.renderSystemSignalPage();
        return '<div class="card"><div class="card-header">信号检测</div><div class="card-body">页面加载中...</div></div>';
    },

    renderSystemRebootPage: function() {
        if (window.UIPage) return UIPage.renderSystemRebootPage();
        return '<div class="card"><div class="card-header">系统重启</div><div class="card-body">页面加载中...</div></div>';
    },

    renderSystemResetPage: function() {
        if (window.UIPage) return UIPage.renderSystemResetPage();
        return '<div class="card"><div class="card-header">配置重置</div><div class="card-body">页面加载中...</div></div>';
    },

    renderBoardNTPPage: function() {
        if (window.UIPage) return UIPage.renderBoardNTPPage();
        return '<div class="card"><div class="card-header">NTP时间同步</div><div class="card-body">页面加载中...</div></div>';
    },

    renderAuthPage: function() {
        if (window.UIPage) return UIPage.renderAuthPage();
        return '<div class="card"><div class="card-header">密码修改</div><div class="card-body">页面加载中...</div></div>';
    },

    renderDefaultPage: function() {
        if (window.UIPage) return UIPage.renderDefaultPage();
        return '<div class="card"><div class="card-header">欢迎</div><div class="card-body">欢迎使用VICTEL IP交换机配置系统</div></div>';
    }
};

// 导出到全局作用域，保持向后兼容
window.UI = UI;
