/**
 * 主应用模块
 * 应用程序入口和全局管理
 * 按照重构设计方案实现现代化前端应用架构
 */

const App = {
    // 应用配置
    config: {
        version: '2.0.0',
        apiTimeout: 30000,
        autoSaveInterval: 30000,
        theme: 'default'
    },

    // 应用状态
    state: {
        initialized: false,
        connected: true,
        currentUser: 'admin',
        lastActivity: Date.now()
    },

    /**
     * 应用初始化
     */
    init: async function() {
        try {
            console.log('正在初始化VICTEL IP交换机配置系统...');
            
            // 检查登录状态
            if (!this.checkLoginState()) {
                return; // 如果未登录，则停止初始化
            }
            
            // 检查浏览器兼容性
            this.checkBrowserCompatibility();
            
            // 加载用户配置
            this.loadUserConfig();
            
            // 初始化各个模块
            await this.initializeModules();
            
            // 绑定全局事件
            this.bindGlobalEvents();
            
            // 启动应用
            this.startApplication();
            
            // 标记为已初始化
            this.state.initialized = true;
            
            console.log('应用初始化完成');
            
        } catch (error) {
            console.error('应用初始化失败:', error);
            this.handleInitializationError(error);
        }
    },

    /**
     * 检查登录状态
     * @returns {boolean} - 如果已登录则返回true，否则返回false
     */
    checkLoginState: function() {
        // 确保auth对象已经加载
        if (!window.auth) {
            console.error('Auth对象未加载');
            setTimeout(() => {
                window.location.href = '/login.html';
            }, 100);
            return false;
        }

        const isLoggedIn = window.auth.isLoggedIn();
        console.log('登录状态检查:', isLoggedIn, 'Token:', window.auth.getToken());
        
        if (!isLoggedIn) {
            console.log('用户未登录，跳转到登录页面');
            window.location.href = '/login.html';
            return false;
        } else {
            const user = window.auth.getUser();
            this.state.currentUser = user?.username || '用户';
            console.log('用户已登录:', this.state.currentUser);
            return true;
        }
    },

    /**
     * 检查浏览器兼容性
     */
    checkBrowserCompatibility: function() {
        const requiredFeatures = [
            'fetch',
            'Promise',
            'localStorage',
            'addEventListener'
        ];

        const missingFeatures = requiredFeatures.filter(feature => {
            return !(feature in window);
        });

        if (missingFeatures.length > 0) {
            throw new Error(`浏览器不支持以下功能: ${missingFeatures.join(', ')}`);
        }

        // 检查CSS Grid支持
        if (!CSS.supports('display', 'grid')) {
            console.warn('浏览器不支持CSS Grid，可能影响布局效果');
        }
    },

    /**
     * 加载用户配置
     */
    loadUserConfig: function() {
        const savedConfig = Utils.storage.get('app_config', {});
        this.config = { ...this.config, ...savedConfig };
        
        const savedState = Utils.storage.get('app_state', {});
        this.state = { ...this.state, ...savedState };
    },

    /**
     * 保存用户配置
     */
    saveUserConfig: function() {
        Utils.storage.set('app_config', this.config);
        Utils.storage.set('app_state', {
            currentUser: this.state.currentUser,
            lastActivity: this.state.lastActivity
        });
    },

    /**
     * 初始化各个模块
     */
    initializeModules: async function() {
        // 初始化UI管理器
        if (window.UI) {
            UI.init({
                currentUser: this.state.currentUser
            });
        }

        // 初始化路由器
        if (window.Router) {
            Router.init();
        }

        // 检查API连接
        await this.checkAPIConnection();
    },

    /**
     * 检查API连接
     */
    checkAPIConnection: async function() {
        try {
            // 使用一个简单的fetch来测试连接，不依赖特定API端点
            const response = await fetch('/api/v1/auth/login', {
                method: 'HEAD'  // HEAD请求只获取响应头，不获取响应体
            });
            // 只要能连接到服务器就认为连接正常（不管是200还是405都说明服务器在线）
            this.state.connected = true;
            if (window.UI) {
                UI.setConnectionStatus(true);
            }
        } catch (error) {
            console.warn('API连接检查失败:', error);
            this.state.connected = false;
            if (window.UI) {
                UI.setConnectionStatus(false);
                UI.showNotification('与服务器连接失败，请检查网络连接', 'warning');
            }
        }
    },

    /**
     * 绑定全局事件
     */
    bindGlobalEvents: function() {
        // 监听窗口关闭事件
        window.addEventListener('beforeunload', (e) => {
            this.saveUserConfig();
        });

        // 监听在线/离线状态
        window.addEventListener('online', () => {
            this.state.connected = true;
            if (window.UI) {
                UI.setConnectionStatus(true);
                UI.showNotification('网络连接已恢复', 'success');
            }
        });

        window.addEventListener('offline', () => {
            this.state.connected = false;
            if (window.UI) {
                UI.setConnectionStatus(false);
                UI.showNotification('网络连接已断开', 'warning');
            }
        });

        // 监听用户活动
        ['click', 'keydown', 'mousemove', 'scroll'].forEach(event => {
            document.addEventListener(event, Utils.throttle(() => {
                this.updateLastActivity();
            }, 1000));
        });

        // 监听路由变化
        window.addEventListener('routechange', (e) => {
            this.onRouteChange(e.detail);
        });

        // 监听错误事件
        window.addEventListener('error', (e) => {
            this.handleGlobalError(e.error);
        });

        // 监听未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (e) => {
            this.handleGlobalError(e.reason);
        });

        // 导航链接点击事件由UINavigation模块处理，避免重复绑定


        // 表单提交事件
        document.addEventListener('submit', (e) => {
            if (e.target.matches('.config-form')) {
                e.preventDefault();
                this.handleFormSubmit(e.target);
            }
        });

        // 通知关闭事件
        document.addEventListener('click', (e) => {
            if (e.target.matches('#notification-close')) {
                if (window.UI) {
                    UI.hideNotification();
                }
            }
        });
    },

    /**
     * 启动应用
     */
    startApplication: function() {
        // 显示应用界面
        document.body.style.visibility = 'visible';
        
        // 启动定期任务
        this.startPeriodicTasks();
        
        // 触发应用启动事件
        const event = new CustomEvent('appstart', {
            detail: { config: this.config, state: this.state }
        });
        window.dispatchEvent(event);
    },

    /**
     * 启动定期任务
     */
    startPeriodicTasks: function() {
        // 定期检查连接状态
        setInterval(() => {
            this.checkAPIConnection();
        }, 30000);

        // 定期保存配置
        setInterval(() => {
            this.saveUserConfig();
        }, this.config.autoSaveInterval);

        // 定期清理过期数据
        setInterval(() => {
            this.cleanupExpiredData();
        }, 300000); // 5分钟
    },

    /**
     * 更新最后活动时间
     */
    updateLastActivity: function() {
        this.state.lastActivity = Date.now();
    },

    /**
     * 路由变化处理
     * @param {Object} detail - 路由详情
     */
    onRouteChange: function(detail) {
        console.log('路由变化:', detail.route);
        this.updateLastActivity();
    },

    /**
     * 处理全局错误
     * @param {Error} error - 错误对象
     */
    handleGlobalError: function(error) {
        console.error('全局错误:', error);
        
        if (window.UI) {
            UI.showNotification('系统发生错误: ' + error.message, 'error');
        }
        
        // 记录错误到本地存储
        const errorLog = Utils.storage.get('error_log', []);
        errorLog.push({
            timestamp: Date.now(),
            message: error.message,
            stack: error.stack,
            url: window.location.href
        });
        
        // 限制错误日志数量
        if (errorLog.length > 100) {
            errorLog.splice(0, 50);
        }
        
        Utils.storage.set('error_log', errorLog);
    },

    /**
     * 处理初始化错误
     * @param {Error} error - 错误对象
     */
    handleInitializationError: function(error) {
        document.body.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 100vh; flex-direction: column; font-family: Arial, sans-serif;">
                <h1 style="color: #dc3545;">应用初始化失败</h1>
                <p style="color: #6c757d; margin-bottom: 2rem;">${error.message}</p>
                <button onclick="location.reload()" style="padding: 0.5rem 1rem; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">重新加载</button>
            </div>
        `;
    },

    /**
     * 清理过期数据
     */
    cleanupExpiredData: function() {
        // 清理过期的错误日志
        const errorLog = Utils.storage.get('error_log', []);
        const oneWeekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
        const filteredLog = errorLog.filter(log => log.timestamp > oneWeekAgo);
        Utils.storage.set('error_log', filteredLog);
    },


    /**
     * 处理表单提交
     * @param {HTMLFormElement} form - 表单元素
     */
    handleFormSubmit: function(form) {
        const formData = new FormData(form);
        const data = {};

        // 转换FormData为普通对象
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }

        // 获取表单的action属性或data-action属性
        const action = form.getAttribute('action') || form.getAttribute('data-action');

        if (action && window.API) {
            // 显示加载状态
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn ? submitBtn.textContent : '';
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.textContent = '保存中...';
            }

            // 发送API请求
            API.post(action, data)
                .then(response => {
                    if (window.UI) {
                        UI.showNotification('配置保存成功', 'success');
                    }
                })
                .catch(error => {
                    console.error('表单提交失败:', error);
                    if (window.UI) {
                        UI.showNotification('配置保存失败: ' + error.message, 'error');
                    }
                })
                .finally(() => {
                    // 恢复按钮状态
                    if (submitBtn) {
                        submitBtn.disabled = false;
                        submitBtn.textContent = originalText;
                    }
                });
        }
    },

    /**
     * 获取应用信息
     * @returns {Object} 应用信息
     */
    getAppInfo: function() {
        return {
            version: this.config.version,
            initialized: this.state.initialized,
            connected: this.state.connected,
            currentUser: this.state.currentUser,
            lastActivity: new Date(this.state.lastActivity).toLocaleString()
        };
    }
};

// 当DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    App.init();
});

// 导出到全局作用域
window.App = App;
