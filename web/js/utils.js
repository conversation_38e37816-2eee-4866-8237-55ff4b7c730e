/**
 * 工具函数模块
 * 提供通用的工具函数和辅助方法
 */

const Utils = {
    /**
     * 验证IP地址格式
     * @param {string} ip - IP地址字符串
     * @returns {boolean} 是否有效
     */
    validateIP: function(ip) {
        const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
        return ipRegex.test(ip);
    },

    /**
     * 验证子网掩码格式
     * @param {string} mask - 子网掩码字符串
     * @returns {boolean} 是否有效
     */
    validateSubnetMask: function(mask) {
        if (!this.validateIP(mask)) return false;
        
        const parts = mask.split('.').map(Number);
        const binary = parts.map(part => part.toString(2).padStart(8, '0')).join('');
        
        // 检查是否为连续的1后跟连续的0
        const match = binary.match(/^(1*)(0*)$/);
        return match && match[1].length > 0;
    },

    /**
     * 验证MAC地址格式
     * @param {string} mac - MAC地址字符串
     * @returns {boolean} 是否有效
     */
    validateMAC: function(mac) {
        const macRegex = /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/;
        return macRegex.test(mac);
    },

    /**
     * 验证子网掩码格式
     * @param {string} mask - 子网掩码
     * @returns {boolean} 验证结果
     */
    validateSubnetMask: function(mask) {
        // 首先验证IP格式
        if (!this.validateIP(mask)) {
            return false;
        }

        // 验证子网掩码的有效性
        const parts = mask.split('.');
        const binary = parts.map(part => {
            return parseInt(part).toString(2).padStart(8, '0');
        }).join('');

        // 子网掩码必须是连续的1后跟连续的0
        const match = binary.match(/^(1*)(0*)$/);
        return match !== null;
    },

    /**
     * 验证端口号
     * @param {number|string} port - 端口号
     * @returns {boolean} 是否有效
     */
    validatePort: function(port) {
        const portNum = parseInt(port);
        return !isNaN(portNum) && portNum >= 1 && portNum <= 65535;
    },

    /**
     * 验证邮箱格式
     * @param {string} email - 邮箱地址
     * @returns {boolean} 是否有效
     */
    validateEmail: function(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    /**
     * 格式化文件大小
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的大小
     */
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * 格式化时间
     * @param {Date|string|number} date - 日期对象或时间戳
     * @returns {string} 格式化后的时间
     */
    formatDateTime: function(date) {
        const d = new Date(date);
        if (isNaN(d.getTime())) return '无效时间';
        
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');
        
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    /**
     * 深拷贝对象
     * @param {any} obj - 要拷贝的对象
     * @returns {any} 拷贝后的对象
     */
    deepClone: function(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const cloned = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    cloned[key] = this.deepClone(obj[key]);
                }
            }
            return cloned;
        }
    },

    /**
     * 防抖函数
     * @param {Function} func - 要防抖的函数
     * @param {number} wait - 等待时间(毫秒)
     * @returns {Function} 防抖后的函数
     */
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    /**
     * 节流函数
     * @param {Function} func - 要节流的函数
     * @param {number} limit - 限制时间(毫秒)
     * @returns {Function} 节流后的函数
     */
    throttle: function(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    /**
     * 生成UUID
     * @returns {string} UUID字符串
     */
    generateUUID: function() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });
    },

    /**
     * 转义HTML字符
     * @param {string} text - 要转义的文本
     * @returns {string} 转义后的文本
     */
    escapeHTML: function(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    },

    /**
     * 获取URL参数
     * @param {string} name - 参数名
     * @returns {string|null} 参数值
     */
    getURLParameter: function(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
    },

    /**
     * 设置URL参数
     * @param {string} name - 参数名
     * @param {string} value - 参数值
     */
    setURLParameter: function(name, value) {
        const url = new URL(window.location);
        url.searchParams.set(name, value);
        window.history.replaceState({}, '', url);
    },

    /**
     * 本地存储操作
     */
    storage: {
        /**
         * 设置本地存储
         * @param {string} key - 键名
         * @param {any} value - 值
         */
        set: function(key, value) {
            try {
                localStorage.setItem(key, JSON.stringify(value));
            } catch (e) {
                console.error('localStorage set error:', e);
            }
        },

        /**
         * 获取本地存储
         * @param {string} key - 键名
         * @param {any} defaultValue - 默认值
         * @returns {any} 存储的值
         */
        get: function(key, defaultValue = null) {
            try {
                const item = localStorage.getItem(key);
                return item ? JSON.parse(item) : defaultValue;
            } catch (e) {
                console.error('localStorage get error:', e);
                return defaultValue;
            }
        },

        /**
         * 删除本地存储
         * @param {string} key - 键名
         */
        remove: function(key) {
            try {
                localStorage.removeItem(key);
            } catch (e) {
                console.error('localStorage remove error:', e);
            }
        },

        /**
         * 清空本地存储
         */
        clear: function() {
            try {
                localStorage.clear();
            } catch (e) {
                console.error('localStorage clear error:', e);
            }
        }
    },

    /**
     * DOM操作辅助函数
     */
    dom: {
        /**
         * 查找元素
         * @param {string} selector - 选择器
         * @param {Element} parent - 父元素
         * @returns {Element|null} 找到的元素
         */
        find: function(selector, parent = document) {
            return parent.querySelector(selector);
        },

        /**
         * 查找所有元素
         * @param {string} selector - 选择器
         * @param {Element} parent - 父元素
         * @returns {NodeList} 找到的元素列表
         */
        findAll: function(selector, parent = document) {
            return parent.querySelectorAll(selector);
        },

        /**
         * 添加类名
         * @param {Element} element - 元素
         * @param {string} className - 类名
         */
        addClass: function(element, className) {
            if (element && className) {
                element.classList.add(className);
            }
        },

        /**
         * 移除类名
         * @param {Element} element - 元素
         * @param {string} className - 类名
         */
        removeClass: function(element, className) {
            if (element && className) {
                element.classList.remove(className);
            }
        },

        /**
         * 切换类名
         * @param {Element} element - 元素
         * @param {string} className - 类名
         */
        toggleClass: function(element, className) {
            if (element && className) {
                element.classList.toggle(className);
            }
        },

        /**
         * 检查是否有类名
         * @param {Element} element - 元素
         * @param {string} className - 类名
         * @returns {boolean} 是否有该类名
         */
        hasClass: function(element, className) {
            return element && className && element.classList.contains(className);
        }
    },

    /**
     * 表单操作辅助函数
     */
    form: {
        /**
         * 获取表单数据
         * @param {Element} form - 表单元素
         * @returns {Object} 表单数据对象
         */
        getFormData: function(form) {
            if (!form) return {};
            
            const formData = new FormData(form);
            const data = {};
            
            for (const [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            return data;
        },

        /**
         * 设置表单字段值
         * @param {Element} form - 表单元素
         * @param {string} name - 字段名
         * @param {any} value - 字段值
         */
        setFieldValue: function(form, name, value) {
            if (!form || !name) return;
            
            const field = form.querySelector(`[name="${name}"]`);
            if (!field) return;
            
            if (field.type === 'checkbox' || field.type === 'radio') {
                field.checked = Boolean(value);
            } else {
                field.value = value || '';
            }
        },

        /**
         * 获取表单字段值
         * @param {Element} form - 表单元素
         * @param {string} name - 字段名
         * @returns {any} 字段值
         */
        getFieldValue: function(form, name) {
            if (!form || !name) return null;
            
            const field = form.querySelector(`[name="${name}"]`);
            if (!field) return null;
            
            if (field.type === 'checkbox') {
                return field.checked;
            } else if (field.type === 'radio') {
                const checked = form.querySelector(`[name="${name}"]:checked`);
                return checked ? checked.value : null;
            } else {
                return field.value;
            }
        },

        /**
         * 重置表单
         * @param {Element} form - 表单元素
         */
        resetForm: function(form) {
            if (form && typeof form.reset === 'function') {
                form.reset();
            }
        }
    },

    /**
     * 表单验证辅助函数
     */
    validation: {
        /**
         * 验证表单
         * @param {Element} form - 表单元素
         * @returns {boolean} 验证是否通过
         */
        validateForm: function(form) {
            if (!form) return false;
            
            // 使用HTML5原生验证
            if (typeof form.checkValidity === 'function') {
                return form.checkValidity();
            }
            
            // 手动验证必填字段
            const requiredFields = form.querySelectorAll('[required]');
            for (const field of requiredFields) {
                if (!field.value.trim()) {
                    return false;
                }
            }
            
            return true;
        },

        /**
         * 绑定表单验证事件
         * @param {string} selector - 表单选择器
         */
        bindFormValidation: function(selector) {
            const form = Utils.dom.find(selector);
            if (!form) return;
            
            // 绑定实时验证事件
            const fields = form.querySelectorAll('input, select, textarea');
            fields.forEach(field => {
                field.addEventListener('blur', () => {
                    this.validateField(field);
                });
                
                field.addEventListener('input', () => {
                    this.clearFieldError(field);
                });
            });
        },

        /**
         * 验证单个字段
         * @param {Element} field - 字段元素
         * @returns {boolean} 验证是否通过
         */
        validateField: function(field) {
            if (!field) return false;
            
            let isValid = true;
            
            // 检查必填项
            if (field.hasAttribute('required') && !field.value.trim()) {
                this.showFieldError(field, '此字段为必填项');
                isValid = false;
            }
            // 检查类型验证
            else if (field.type === 'email' && field.value && !Utils.validateEmail(field.value)) {
                this.showFieldError(field, '请输入有效的邮箱地址');
                isValid = false;
            }
            // 检查IP地址
            else if (field.classList.contains('ip-field') && field.value && !Utils.validateIP(field.value)) {
                this.showFieldError(field, '请输入有效的IP地址');
                isValid = false;
            }
            // 检查端口号
            else if (field.type === 'number' && field.value && !Utils.validatePort(field.value)) {
                this.showFieldError(field, '请输入有效的端口号(1-65535)');
                isValid = false;
            }
            
            if (isValid) {
                this.clearFieldError(field);
            }
            
            return isValid;
        },

        /**
         * 显示字段错误
         * @param {Element} field - 字段元素
         * @param {string} message - 错误消息
         */
        showFieldError: function(field, message) {
            if (!field) return;
            
            this.clearFieldError(field);
            
            field.classList.add('error');
            
            const errorElement = document.createElement('small');
            errorElement.className = 'field-error';
            errorElement.textContent = message;
            
            field.parentNode.appendChild(errorElement);
        },

        /**
         * 清除字段错误
         * @param {Element} field - 字段元素
         */
        clearFieldError: function(field) {
            if (!field) return;
            
            field.classList.remove('error');
            
            const errorElement = field.parentNode.querySelector('.field-error');
            if (errorElement) {
                errorElement.remove();
                         }
         }
     },

    /**
     * 导航辅助函数
     */
    navigation: {
        /**
         * 绑定导航事件
         */
        bindNavigationEvents: function() {
            // 绑定所有导航链接的点击事件
            const navLinks = Utils.dom.findAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    const page = link.dataset.page;
                    if (page && window.Router) {
                        Router.navigate(page);
                    }
                });
            });
        }
    }
};
