/**
 * 简化的应用管理器
 * 专注于核心初始化功能，移除冗余的事件绑定和复杂逻辑
 */
const AppSimplified = {
    // 应用状态
    state: {
        initialized: false,
        currentUser: 'admin'
    },

    /**
     * 应用初始化
     */
    init: async function() {
        try {
            console.log('正在初始化VICTEL IP交换机配置系统...');
            
            // 检查登录状态
            if (!this.checkLoginState()) {
                return; // 如果未登录，则停止初始化
            }
            
            // 初始化UI
            this.initializeUI();
            
            // 标记为已初始化
            this.state.initialized = true;
            
            console.log('应用初始化完成');
            
        } catch (error) {
            console.error('应用初始化失败:', error);
            this.handleInitializationError(error);
        }
    },

    /**
     * 检查登录状态
     * @returns {boolean} - 如果已登录则返回true，否则返回false
     */
    checkLoginState: function() {
        // 确保auth对象已经加载
        if (!window.auth) {
            console.error('Auth对象未加载');
            setTimeout(() => {
                window.location.href = '/login.html';
            }, 100);
            return false;
        }

        const isLoggedIn = window.auth.isLoggedIn();
        console.log('登录状态检查:', isLoggedIn);
        
        if (!isLoggedIn) {
            console.log('用户未登录，跳转到登录页面');
            window.location.href = '/login.html';
            return false;
        } else {
            const user = window.auth.getUser();
            this.state.currentUser = user?.username || '用户';
            console.log('用户已登录:', this.state.currentUser);
            
            // 更新用户显示
            const userEl = document.getElementById('current-user');
            if (userEl) {
                userEl.textContent = this.state.currentUser;
            }
            
            return true;
        }
    },

    /**
     * 初始化UI
     */
    initializeUI: function() {
        if (window.UI) {
            UI.init();
        } else {
            console.error('UI模块未加载');
        }
    },

    /**
     * 处理初始化错误
     * @param {Error} error - 错误对象
     */
    handleInitializationError: function(error) {
        document.body.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 100vh; flex-direction: column; font-family: Arial, sans-serif;">
                <h1 style="color: #dc3545;">应用初始化失败</h1>
                <p style="color: #6c757d; margin-bottom: 2rem;">${error.message}</p>
                <button onclick="location.reload()" style="padding: 0.5rem 1rem; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">重新加载</button>
            </div>
        `;
    }
};

// 当DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    // 添加一个小延迟确保所有脚本都已加载
    setTimeout(() => {
        AppSimplified.init();
    }, 100);
});

// 导出到全局作用域
window.App = AppSimplified; 