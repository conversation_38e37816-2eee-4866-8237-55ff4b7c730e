/**
 * UI模态框管理模块
 * 负责模态框的显示、隐藏和交互管理
 * 从ui.js拆分出来，专门处理模态框相关的逻辑
 */

const UIModal = {
    /**
     * 初始化模态框管理器
     */
    init: function() {
        this.bindModalEvents();
    },

    /**
     * 绑定模态框相关事件
     */
    bindModalEvents: function() {
        const elements = UICore.getAllElements();

        // 模态框关闭按钮事件
        if (elements.modalClose) {
            elements.modalClose.addEventListener('click', () => {
                this.hide();
            });
        }

        // 模态框取消按钮事件
        if (elements.modalCancel) {
            elements.modalCancel.addEventListener('click', () => {
                this.hide();
            });
        }

        // 点击模态框背景关闭
        if (elements.modal) {
            elements.modal.addEventListener('click', (e) => {
                if (e.target === elements.modal) {
                    this.hide();
                }
            });
        }
    },

    /**
     * 显示模态框
     * @param {string} title - 标题
     * @param {string} content - 内容
     * @param {Function} onConfirm - 确认回调
     */
    show: function(title, content, onConfirm = null) {
        const elements = UICore.getAllElements();
        
        if (elements.modal) {
            // 设置标题和内容
            if (elements.modalTitle) {
                elements.modalTitle.textContent = title;
            }
            if (elements.modalBody) {
                elements.modalBody.innerHTML = content;
            }

            // 设置确认按钮事件
            if (elements.modalConfirm) {
                elements.modalConfirm.onclick = () => {
                    if (onConfirm) onConfirm();
                    this.hide();
                };
            }

            // 显示模态框
            Utils.dom.addClass(elements.modal, 'show');
        }
    },

    /**
     * 隐藏模态框
     */
    hide: function() {
        const modal = UICore.getElement('modal');
        if (modal) {
            Utils.dom.removeClass(modal, 'show');
        }
    },

    /**
     * 显示确认对话框
     * @param {string} title - 标题
     * @param {string} message - 消息内容
     * @param {Function} onConfirm - 确认回调
     * @param {Function} onCancel - 取消回调
     */
    confirm: function(title, message, onConfirm = null, onCancel = null) {
        this.show(title, message, onConfirm);
        
        // 如果有取消回调，绑定到取消按钮
        if (onCancel) {
            const modalCancel = UICore.getElement('modalCancel');
            if (modalCancel) {
                modalCancel.onclick = () => {
                    onCancel();
                    this.hide();
                };
            }
        }
    },

    /**
     * 显示系统重启确认对话框
     */
    confirmReboot: function() {
        this.show(
            '确认重启',
            '您确定要重启系统吗？重启后需要等待约2分钟才能重新访问。',
            async () => {
                try {
                    await API.system.reboot();
                    if (window.UINotification) {
                        UINotification.show('系统重启命令已发送', 'success');
                    }
                } catch (error) {
                    if (window.UINotification) {
                        UINotification.show('重启失败: ' + error.message, 'error');
                    }
                }
            }
        );
    },

    /**
     * 显示配置重置确认对话框
     */
    confirmReset: function() {
        this.show(
            '确认重置',
            '您确定要重置所有配置吗？此操作将恢复出厂设置，无法撤销。',
            async () => {
                try {
                    await API.system.reset();
                    if (window.UINotification) {
                        UINotification.show('配置重置成功', 'success');
                    }
                } catch (error) {
                    if (window.UINotification) {
                        UINotification.show('重置失败: ' + error.message, 'error');
                    }
                }
            }
        );
    },

    /**
     * 检查模态框是否显示
     * @returns {boolean} 是否显示
     */
    isVisible: function() {
        const modal = UICore.getElement('modal');
        return modal && Utils.dom.hasClass(modal, 'show');
    }
};

// 导出到全局作用域，保持向后兼容
window.UIModal = UIModal;
