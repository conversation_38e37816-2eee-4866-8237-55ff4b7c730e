/**
 * UI核心管理模块
 * 负责UI的初始化、元素缓存和基础状态管理
 * 从ui.js拆分出来，保持核心功能的独立性
 */

const UICore = {
    // UI元素缓存
    elements: {},

    // 当前页面状态
    currentPage: 'network-selection',

    /**
     * 初始化UI核心管理器
     */
    init: function() {
        this.cacheElements();
        this.initializeComponents();
        this.initMobileMenu();
        
        // 初始化其他UI模块
        if (window.UINavigation) UINavigation.init();
        if (window.UIModal) UIModal.init();
        if (window.UINotification) UINotification.init();
        if (window.UIPage) UIPage.init();
    },

    /**
     * 缓存常用DOM元素
     */
    cacheElements: function() {
        this.elements = {
            // 主要容器
            mainContent: Utils.dom.find('#main-content'),
            loading: Utils.dom.find('#loading'),
            breadcrumb: Utils.dom.find('#breadcrumb-text'),
            statusText: Utils.dom.find('#status-text'),
            connectionStatus: Utils.dom.find('#connection-status'),

            // 导航菜单
            navMenu: Utils.dom.find('#nav-menu'),
            navLinks: Utils.dom.findAll('.nav-link'),
            sidebar: Utils.dom.find('.app-sidebar'),

            // 模态框
            modal: Utils.dom.find('#modal'),
            modalTitle: Utils.dom.find('#modal-title'),
            modalBody: Utils.dom.find('#modal-body'),
            modalClose: Utils.dom.find('#modal-close'),
            modalCancel: Utils.dom.find('#modal-cancel'),
            modalConfirm: Utils.dom.find('#modal-confirm'),

            // 通知
            notification: Utils.dom.find('#notification'),
            notificationText: Utils.dom.find('#notification-text'),
            notificationClose: Utils.dom.find('#notification-close'),

            // 其他
            logoutBtn: Utils.dom.find('#logout-btn')
        };
    },

    /**
     * 初始化组件
     */
    initializeComponents: function() {
        // 设置初始状态
        this.setStatus('就绪');
        this.setConnectionStatus(true);

        // 检查DOM元素是否正确缓存
        if (!this.elements.mainContent) {
            console.error('主内容容器未找到');
            return;
        }

        console.log('UI核心组件初始化完成');
    },

    /**
     * 初始化移动端菜单
     */
    initMobileMenu: function() {
        // 创建菜单切换按钮
        const header = Utils.dom.find('.app-header');
        if (header && window.innerWidth <= 768) {
            const menuToggle = document.createElement('button');
            menuToggle.className = 'menu-toggle';
            menuToggle.innerHTML = '☰';
            menuToggle.addEventListener('click', () => {
                Utils.dom.toggleClass(this.elements.sidebar, 'active');
            });

            const headerBrand = Utils.dom.find('.header-brand');
            if (headerBrand) {
                headerBrand.insertBefore(menuToggle, headerBrand.firstChild);
            }
        }

        // 监听窗口大小变化
        window.addEventListener('resize', Utils.debounce(() => {
            if (window.innerWidth > 768) {
                Utils.dom.removeClass(this.elements.sidebar, 'active');
            }
        }, 250));
    },

    /**
     * 显示加载动画
     */
    showLoading: function() {
        if (this.elements.loading) {
            Utils.dom.removeClass(this.elements.loading, 'hidden');
        }
    },

    /**
     * 隐藏加载动画
     */
    hideLoading: function() {
        if (this.elements.loading) {
            Utils.dom.addClass(this.elements.loading, 'hidden');
        }
    },

    /**
     * 设置状态文本
     * @param {string} text - 状态文本
     */
    setStatus: function(text) {
        if (this.elements.statusText) {
            this.elements.statusText.textContent = text;
        }
    },

    /**
     * 设置连接状态
     * @param {boolean} connected - 是否连接
     */
    setConnectionStatus: function(connected) {
        if (this.elements.connectionStatus) {
            this.elements.connectionStatus.textContent = connected ? '已连接' : '未连接';
            this.elements.connectionStatus.className = connected ? 'status-connected' : 'status-disconnected';
        }
    },

    /**
     * 获取缓存的DOM元素
     * @param {string} key - 元素键名
     * @returns {Element|null} DOM元素
     */
    getElement: function(key) {
        return this.elements[key] || null;
    },

    /**
     * 获取所有缓存的DOM元素
     * @returns {Object} 所有缓存的元素
     */
    getAllElements: function() {
        return this.elements;
    }
};

// 导出到全局作用域，保持向后兼容
window.UICore = UICore;
