/**
 * UI通知管理模块
 * 负责通知的显示、隐藏和自动管理
 * 从ui.js拆分出来，专门处理通知相关的逻辑
 */

const UINotification = {
    // 当前通知的定时器
    currentTimer: null,

    /**
     * 初始化通知管理器
     */
    init: function() {
        this.bindNotificationEvents();
    },

    /**
     * 绑定通知相关事件
     */
    bindNotificationEvents: function() {
        const notificationClose = UICore.getElement('notificationClose');
        
        // 通知关闭按钮事件
        if (notificationClose) {
            notificationClose.addEventListener('click', () => {
                this.hide();
            });
        }
    },

    /**
     * 显示通知
     * @param {string} message - 消息内容
     * @param {string} type - 通知类型 (success, error, warning, info)
     * @param {number} duration - 显示时长(毫秒)
     */
    show: function(message, type = 'info', duration = 3000) {
        const elements = UICore.getAllElements();
        
        if (elements.notification) {
            // 清除之前的定时器
            if (this.currentTimer) {
                clearTimeout(this.currentTimer);
                this.currentTimer = null;
            }

            // 设置通知内容
            if (elements.notificationText) {
                elements.notificationText.textContent = message;
            }
            
            // 设置通知类型和显示状态
            elements.notification.className = `notification ${type} show`;

            // 自动隐藏
            if (duration > 0) {
                this.currentTimer = setTimeout(() => {
                    this.hide();
                }, duration);
            }
        }
    },

    /**
     * 隐藏通知
     */
    hide: function() {
        const notification = UICore.getElement('notification');
        
        if (notification) {
            Utils.dom.removeClass(notification, 'show');
        }

        // 清除定时器
        if (this.currentTimer) {
            clearTimeout(this.currentTimer);
            this.currentTimer = null;
        }
    },

    /**
     * 显示成功通知
     * @param {string} message - 消息内容
     * @param {number} duration - 显示时长(毫秒)
     */
    success: function(message, duration = 3000) {
        this.show(message, 'success', duration);
    },

    /**
     * 显示错误通知
     * @param {string} message - 消息内容
     * @param {number} duration - 显示时长(毫秒)
     */
    error: function(message, duration = 5000) {
        this.show(message, 'error', duration);
    },

    /**
     * 显示警告通知
     * @param {string} message - 消息内容
     * @param {number} duration - 显示时长(毫秒)
     */
    warning: function(message, duration = 4000) {
        this.show(message, 'warning', duration);
    },

    /**
     * 显示信息通知
     * @param {string} message - 消息内容
     * @param {number} duration - 显示时长(毫秒)
     */
    info: function(message, duration = 3000) {
        this.show(message, 'info', duration);
    },

    /**
     * 检查通知是否显示
     * @returns {boolean} 是否显示
     */
    isVisible: function() {
        const notification = UICore.getElement('notification');
        return notification && Utils.dom.hasClass(notification, 'show');
    },

    /**
     * 清除所有通知
     */
    clear: function() {
        this.hide();
    }
};

// 导出到全局作用域，保持向后兼容
window.UINotification = UINotification;
