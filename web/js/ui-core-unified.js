/**
 * 统一的UI核心模块
 * 合并了ui-core.js, ui-modal.js, ui-notification.js等模块
 * 精简代码结构，减少冗余
 */
const UIUnified = {
    // DOM元素缓存
    elements: {},
    
    // 当前页面状态
    currentPage: 'network-selection',
    
    /**
     * 初始化UI管理器
     */
    init: function() {
        this.cacheElements();
        this.initializeComponents();
        this.bindEvents();
        
        console.log('统一UI模块初始化完成');
    },

    /**
     * 缓存常用DOM元素
     */
    cacheElements: function() {
        this.elements = {
            // 主要容器
            mainContent: document.getElementById('main-content'),
            loading: document.getElementById('loading'),
            breadcrumb: document.getElementById('breadcrumb-text'),
            statusText: document.getElementById('status-text'),
            connectionStatus: document.getElementById('connection-status'),

            // 导航菜单
            navMenu: document.getElementById('nav-menu'),
            navLinks: document.querySelectorAll('.nav-link'),
            sidebar: document.querySelector('.app-sidebar'),

            // 模态框
            modal: document.getElementById('modal'),
            modalTitle: document.getElementById('modal-title'),
            modalBody: document.getElementById('modal-body'),
            modalClose: document.getElementById('modal-close'),
            modalCancel: document.getElementById('modal-cancel'),
            modalConfirm: document.getElementById('modal-confirm'),

            // 通知
            notification: document.getElementById('notification'),
            notificationText: document.getElementById('notification-text'),
            notificationClose: document.getElementById('notification-close'),

            // 其他
            logoutBtn: document.getElementById('logout-btn')
        };
    },

    /**
     * 初始化组件
     */
    initializeComponents: function() {
        this.setStatus('就绪');
        this.setConnectionStatus(true);
        
        if (!this.elements.mainContent) {
            console.error('主内容容器未找到');
            return;
        }
    },

    /**
     * 绑定事件
     */
    bindEvents: function() {
        // 导航菜单点击事件
        if (this.elements.navLinks) {
            this.elements.navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    const page = link.dataset.page;
                    if (page) {
                        this.navigateToPage(page);
                        this.setActiveNavLink(link);
                    }
                });
            });
        }

        // 退出登录事件
        if (this.elements.logoutBtn) {
            this.elements.logoutBtn.addEventListener('click', () => {
                this.confirmLogout();
            });
        }

        // 模态框事件
        if (this.elements.modalClose) {
            this.elements.modalClose.addEventListener('click', () => {
                this.hideModal();
            });
        }
        
        if (this.elements.modalCancel) {
            this.elements.modalCancel.addEventListener('click', () => {
                this.hideModal();
            });
        }

        // 通知关闭事件
        if (this.elements.notificationClose) {
            this.elements.notificationClose.addEventListener('click', () => {
                this.hideNotification();
            });
        }

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideModal();
                this.hideNotification();
            }
        });
    },

    // === 导航功能 ===
    
    /**
     * 导航到指定页面
     * @param {string} page - 页面名称
     */
    navigateToPage: function(page) {
        this.currentPage = page;
        this.showLoading();
        this.updateBreadcrumb(page);
        
        // 渲染页面内容
        this.renderPageContent(page);
        
        this.hideLoading();
    },

    /**
     * 设置活动导航链接
     * @param {Element} activeLink - 活动链接元素
     */
    setActiveNavLink: function(activeLink) {
        if (!this.elements.navLinks) return;

        // 移除所有活动状态
        this.elements.navLinks.forEach(link => {
            link.classList.remove('active');
        });
        
        // 设置当前活动状态
        activeLink.classList.add('active');
        
        // 展开父级菜单
        const parentItem = activeLink.closest('.nav-item');
        if (parentItem) {
            parentItem.classList.add('active');
        }
    },

    /**
     * 更新面包屑导航
     * @param {string} page - 页面名称
     */
    updateBreadcrumb: function(page) {
        const breadcrumbMap = {
            'network-selection': '网络配置 > 网络选择',
            'network-ethernet': '网络配置 > 以太网配置',
            'center-config': '设备配置 > 呼叫中心',
            'gateway-config': '设备配置 > 互联网关',
            'recorder-config': '设备配置 > 录音配置',
            'sci-config': '设备配置 > 基站配置',
            'switch-config': '设备配置 > 交换配置',
            'ntp-config': '时间同步',
            'auth-config': '密码修改'
        };
        
        if (this.elements.breadcrumb) {
            this.elements.breadcrumb.textContent = breadcrumbMap[page] || page;
        }
    },

    // === 页面渲染功能 ===
    
    /**
     * 渲染页面内容
     * @param {string} page - 页面名称
     */
    renderPageContent: function(page) {
        let content = '';
        
        // 调用对应的页面模块
        const pageModuleMap = {
            'network-ethernet': window.BoardEthernetPage,
            'center-config': window.CenterConfigPage,
            'gateway-config': window.GatewayConfigPage,
            'recorder-config': window.RecorderConfigPage,
            'sci-config': window.SCIConfigPage,
            'switch-config': window.SwitchConfigPage,
            'ntp-config': window.BoardNTPPage,
            'auth-config': window.AuthConfigPage,
            'device-config': window.DeviceConfigPage,
            'system-management': window.SystemManagementPage
        };
        
        const pageModule = pageModuleMap[page];
        if (pageModule && typeof pageModule.render === 'function') {
            content = pageModule.render();
            setTimeout(() => {
                if (pageModule.init) pageModule.init();
            }, 100);
        } else {
            content = `<div class="card">
                <div class="card-header">${page}</div>
                <div class="card-body">页面内容加载中...</div>
            </div>`;
        }
        
        if (this.elements.mainContent) {
            this.elements.mainContent.innerHTML = content;
        }
    },

    // === 模态框功能 ===
    
    /**
     * 显示模态框
     * @param {string} title - 标题
     * @param {string} content - 内容
     * @param {Function} onConfirm - 确认回调
     */
    showModal: function(title, content, onConfirm) {
        if (!this.elements.modal) return;
        
        this.elements.modalTitle.textContent = title;
        this.elements.modalBody.innerHTML = content;
        this.elements.modal.style.display = 'flex';
        
        if (onConfirm && this.elements.modalConfirm) {
            this.elements.modalConfirm.onclick = () => {
                onConfirm();
                this.hideModal();
            };
        }
    },

    /**
     * 隐藏模态框
     */
    hideModal: function() {
        if (this.elements.modal) {
            this.elements.modal.style.display = 'none';
        }
    },

    /**
     * 确认退出登录
     */
    confirmLogout: function() {
        this.showModal(
            '确认退出',
            '您确定要退出系统吗？',
            () => {
                window.auth.logout();
            }
        );
    },

    // === 通知功能 ===
    
    /**
     * 显示通知
     * @param {string} message - 消息
     * @param {string} type - 类型
     * @param {number} duration - 持续时间
     */
    showNotification: function(message, type = 'info', duration = 3000) {
        if (!this.elements.notification) return;
        
        this.elements.notificationText.textContent = message;
        this.elements.notification.className = `notification ${type}`;
        this.elements.notification.style.display = 'block';
        
        if (duration > 0) {
            setTimeout(() => {
                this.hideNotification();
            }, duration);
        }
    },

    /**
     * 隐藏通知
     */
    hideNotification: function() {
        if (this.elements.notification) {
            this.elements.notification.style.display = 'none';
        }
    },

    // === 工具方法 ===
    
    /**
     * 显示加载动画
     */
    showLoading: function() {
        if (this.elements.loading) {
            this.elements.loading.classList.remove('hidden');
        }
    },

    /**
     * 隐藏加载动画
     */
    hideLoading: function() {
        if (this.elements.loading) {
            this.elements.loading.classList.add('hidden');
        }
    },

    /**
     * 设置状态文本
     * @param {string} text - 状态文本
     */
    setStatus: function(text) {
        if (this.elements.statusText) {
            this.elements.statusText.textContent = text;
        }
    },

    /**
     * 设置连接状态
     * @param {boolean} connected - 是否连接
     */
    setConnectionStatus: function(connected) {
        if (this.elements.connectionStatus) {
            this.elements.connectionStatus.textContent = connected ? '已连接' : '未连接';
            this.elements.connectionStatus.className = connected ? 'status-connected' : 'status-disconnected';
        }
    }
};

// 导出到全局作用域
window.UI = UIUnified;
window.UICore = UIUnified; // 兼容旧代码
window.UIModal = UIUnified; // 兼容旧代码
window.UINotification = UIUnified; // 兼容旧代码
window.UINavigation = UIUnified; // 兼容旧代码 