/**
 * 路由管理模块
 * 负责前端页面路由和状态管理
 * 按照重构设计方案实现单页应用路由
 */

const Router = {
    // 路由配置
    routes: {
        'network-selection': {
            title: '网络选择配置',
            breadcrumb: '网络配置 > 网络选择',
            component: 'NetworkSelection'
        },
        'network-ethernet': {
            title: '以太网配置',
            breadcrumb: '网络配置 > 以太网配置',
            component: 'NetworkEthernet'
        },
        'center-config': {
            title: '呼叫中心配置',
            breadcrumb: '设备配置 > 呼叫中心',
            component: 'CenterConfig'
        },
        'gateway-config': {
            title: '互联网关配置',
            breadcrumb: '设备配置 > 互联网关',
            component: 'GatewayConfig'
        },
        'recorder-config': {
            title: '录音配置',
            breadcrumb: '设备配置 > 录音配置',
            component: 'RecorderConfig'
        },
        'sci-config': {
            title: 'SCI基站配置',
            breadcrumb: '设备配置 > 基站配置',
            component: 'SCIConfig'
        },
        'switch-config': {
            title: '交换机配置',
            breadcrumb: '设备配置 > 交换配置',
            component: 'SwitchConfig'
        },
        'system-logs': {
            title: '系统日志',
            breadcrumb: '系统管理 > 系统日志',
            component: 'SystemLogs'
        },
        'system-signal': {
            title: '信号检测',
            breadcrumb: '系统管理 > 信号检测',
            component: 'SystemSignal'
        },
        'system-reboot': {
            title: '系统重启',
            breadcrumb: '系统管理 > 系统重启',
            component: 'SystemReboot'
        },
        'system-reset': {
            title: '配置重置',
            breadcrumb: '系统管理 > 配置重置',
            component: 'SystemReset'
        },
        'ntp-config': {
            title: 'NTP时间同步',
            breadcrumb: '时间同步',
            component: 'NTPConfig'
        },
        'auth-config': {
            title: '密码修改',
            breadcrumb: '密码修改',
            component: 'AuthConfig'
        }
    },

    // 当前路由
    currentRoute: null,
    
    // 历史记录
    history: [],

    /**
     * 初始化路由器
     */
    init: function() {
        this.bindEvents();
        this.loadInitialRoute();
    },

    /**
     * 绑定路由事件
     */
    bindEvents: function() {
        // 监听浏览器前进后退
        window.addEventListener('popstate', (e) => {
            if (e.state && e.state.route) {
                this.navigateTo(e.state.route, false);
            }
        });

        // 监听hash变化
        window.addEventListener('hashchange', () => {
            this.handleHashChange();
        });
    },

    /**
     * 加载初始路由
     */
    loadInitialRoute: function() {
        const hash = window.location.hash.slice(1);
        const route = hash || 'network-selection';
        this.navigateTo(route, false);
    },

    /**
     * 处理hash变化
     */
    handleHashChange: function() {
        const hash = window.location.hash.slice(1);
        if (hash && this.routes[hash]) {
            this.navigateTo(hash, false);
        }
    },

    /**
     * 导航到指定路由
     * @param {string} route - 路由名称
     * @param {boolean} pushState - 是否推入历史记录
     */
    navigateTo: function(route, pushState = true) {
        if (!this.routes[route]) {
            console.error('Unknown route:', route);
            return;
        }

        const routeConfig = this.routes[route];
        
        // 更新当前路由
        this.currentRoute = route;
        
        // 更新URL
        if (pushState) {
            const url = `#${route}`;
            window.history.pushState({ route: route }, routeConfig.title, url);
        }
        
        // 更新页面标题
        document.title = `${routeConfig.title} - VICTEL IP交换机配置系统`;
        
        // 添加到历史记录
        this.addToHistory(route);
        
        // 触发路由变化事件
        this.onRouteChange(route, routeConfig);
    },

    /**
     * 路由变化处理
     * @param {string} route - 路由名称
     * @param {Object} config - 路由配置
     */
    onRouteChange: function(route, config) {
        // 通知UI管理器更新页面
        if (window.UI) {
            UI.navigateToPage(route);
        }
        
        // 触发自定义事件
        const event = new CustomEvent('routechange', {
            detail: { route, config }
        });
        window.dispatchEvent(event);
    },

    /**
     * 添加到历史记录
     * @param {string} route - 路由名称
     */
    addToHistory: function(route) {
        // 避免重复添加相同路由
        if (this.history[this.history.length - 1] !== route) {
            this.history.push(route);
            
            // 限制历史记录长度
            if (this.history.length > 50) {
                this.history.shift();
            }
        }
    },

    /**
     * 返回上一页
     */
    goBack: function() {
        if (this.history.length > 1) {
            this.history.pop(); // 移除当前页面
            const previousRoute = this.history[this.history.length - 1];
            this.navigateTo(previousRoute);
        } else {
            // 如果没有历史记录，导航到默认页面
            this.navigateTo('network-selection');
        }
    },

    /**
     * 获取当前路由信息
     * @returns {Object} 当前路由配置
     */
    getCurrentRoute: function() {
        return this.routes[this.currentRoute];
    },

    /**
     * 获取路由历史
     * @returns {Array} 历史记录数组
     */
    getHistory: function() {
        return [...this.history];
    },

    /**
     * 清空历史记录
     */
    clearHistory: function() {
        this.history = [];
    },

    /**
     * 检查路由是否存在
     * @param {string} route - 路由名称
     * @returns {boolean} 是否存在
     */
    routeExists: function(route) {
        return !!this.routes[route];
    },

    /**
     * 获取所有路由
     * @returns {Object} 所有路由配置
     */
    getAllRoutes: function() {
        return { ...this.routes };
    },

    /**
     * 动态添加路由
     * @param {string} route - 路由名称
     * @param {Object} config - 路由配置
     */
    addRoute: function(route, config) {
        this.routes[route] = config;
    },

    /**
     * 移除路由
     * @param {string} route - 路由名称
     */
    removeRoute: function(route) {
        delete this.routes[route];
    },

    /**
     * 导航方法别名
     * @param {string} route - 路由名称
     */
    navigate: function(route) {
        this.navigateTo(route);
    }
};
