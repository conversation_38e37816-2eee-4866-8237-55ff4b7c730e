/* 组件样式 - 表单、模态框、通知等 */

/* 表单样式 - 按照重构设计方案实现统一表单组件 */

/* 表单网格布局系统 */
.form-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -0.75rem;
    margin-bottom: 1.5rem;
}

.form-col {
    flex: 1;
    padding: 0 0.75rem;
    min-width: 0; /* 防止flex子项溢出 */
}

.form-col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.form-col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.form-col-3 { flex: 0 0 25%; max-width: 25%; }
.form-col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.form-col-6 { flex: 0 0 50%; max-width: 50%; }
.form-col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.form-col-9 { flex: 0 0 75%; max-width: 75%; }
.form-col-12 { flex: 0 0 100%; max-width: 100%; }

/* 表单组件 */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
    font-size: 0.9rem;
}

.form-label.required::after {
    content: " *";
    color: var(--danger-color);
}

.form-control {
    display: block;
    width: 100%;
    padding: 0.75rem;
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--text-color);
    background-color: var(--white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 166, 166, 0.2);
}

.form-control:invalid {
    border-color: var(--danger-color);
}

.form-control:invalid:focus {
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 16px 12px;
    padding-right: 2.5rem;
}

/* 单选按钮和复选框样式 */
.radio-group {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.radio-item,
.checkbox-item {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background: var(--white);
    transition: var(--transition);
    cursor: pointer;
}

.radio-item:hover,
.checkbox-item:hover {
    border-color: var(--primary-light);
    background: var(--background-secondary);
}

.radio-item input[type="radio"],
.checkbox-item input[type="checkbox"] {
    margin: 0;
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
    cursor: pointer;
    flex-shrink: 0;
}

.radio-label,
.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    cursor: pointer;
    flex: 1;
}

.radio-label span,
.checkbox-text {
    font-weight: 500;
    color: var(--text-color);
    flex: 1;
}

.radio-item:has(input:checked),
.checkbox-item:has(input:checked) {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
}

.form-check {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.form-check-input {
    margin-right: 0.5rem;
}

.form-text,
.form-help {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 0.25rem;
    line-height: 1.4;
}

.form-error {
    color: var(--danger-color);
    font-size: 0.8rem;
    margin-top: 0.25rem;
    display: block;
}

/* 表单验证状态 */
.form-control.is-valid {
    border-color: var(--success-color);
}

.form-control.is-valid:focus {
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.2);
}

.form-control.is-invalid {
    border-color: var(--danger-color);
}

.form-control.is-invalid:focus {
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
}

/* 表单操作按钮组 */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
    margin-top: 2rem;
}

.form-actions.center {
    justify-content: center;
}

.form-actions.start {
    justify-content: flex-start;
}

.form-actions.between {
    justify-content: space-between;
}

/* 配置表单特殊样式 */
.config-form {
    max-width: none;
}

.config-form .card {
    margin-bottom: 2rem;
}

.config-form .card:last-child {
    margin-bottom: 0;
}

/* 表格样式 */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
}

.table th,
.table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    background-color: var(--light-color);
    font-weight: 600;
    color: var(--text-color);
}

.table tbody tr:hover {
    background-color: rgba(0, 166, 166, 0.05);
}

/* 卡片样式 */
.card {
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin-bottom: 1.5rem;
}

.card-header {
    padding: 1rem 1.5rem;
    background-color: var(--light-color);
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

.card-description {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin: 0.5rem 0 0 0;
}

/* 页面容器样式 */
.page-container {
    width: 100%;
    max-width: none;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.page-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-color);
}

.page-actions {
    display: flex;
    gap: 0.75rem;
}

.page-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* 状态网格 */
.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--background-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-light);
}

.status-label {
    font-weight: 500;
    color: var(--text-secondary);
}

.status-value {
    font-weight: 600;
    color: var(--text-color);
}

.card-footer {
    padding: 1rem 1.5rem;
    background-color: var(--light-color);
    border-top: 1px solid var(--border-color);
}

/* 模态框样式 - 现代化设计 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(15, 23, 42, 0.6);
    backdrop-filter: blur(4px);
    z-index: 2000;
    animation: fadeIn 0.3s ease;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideIn 0.3s ease;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    font-size: 1.25rem;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-color);
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 3000;
    transform: translateX(100%);
    transition: var(--transition);
}

.notification.show {
    transform: translateX(0);
}

.notification-content {
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    min-width: 300px;
}

.notification.success .notification-content {
    border-left: 4px solid var(--success-color);
}

.notification.error .notification-content {
    border-left: 4px solid var(--danger-color);
}

.notification.warning .notification-content {
    border-left: 4px solid var(--warning-color);
}

.notification.info .notification-content {
    border-left: 4px solid var(--info-color);
}

.notification-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: var(--text-color);
    padding: 0;
    margin-left: auto;
}

/* 进度条样式 */
.progress {
    height: 20px;
    background-color: var(--light-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-bar {
    height: 100%;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 0.8rem;
}

/* 标签样式 */
.badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
}

.badge-primary {
    background-color: var(--primary-color);
    color: var(--white);
}

.badge-success {
    background-color: var(--success-color);
    color: var(--white);
}

.badge-danger {
    background-color: var(--danger-color);
    color: var(--white);
}

.badge-warning {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

.badge-info {
    background-color: var(--info-color);
    color: var(--white);
}

/* 状态指示器 */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
}

.status-dot.online {
    background-color: var(--success-color);
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.3);
}

.status-dot.offline {
    background-color: var(--danger-color);
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.3);
}

.status-dot.warning {
    background-color: var(--warning-color);
    box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.3);
}

.status-dot.unknown {
    background-color: #6c757d;
    box-shadow: 0 0 0 2px rgba(108, 117, 125, 0.3);
}

/* 系统状态组件 */
.system-status {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.status-card {
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    text-align: center;
    transition: var(--transition);
}

.status-card:hover {
    box-shadow: var(--shadow);
    transform: translateY(-2px);
}

.status-card .status-value {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.status-card .status-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.status-card .status-description {
    font-size: 0.8rem;
    color: #6c757d;
}

.status-card.status-good .status-value {
    color: var(--success-color);
}

.status-card.status-warning .status-value {
    color: var(--warning-color);
}

.status-card.status-danger .status-value {
    color: var(--danger-color);
}

/* 动画 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.text-muted { color: #6c757d; }
.text-primary { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-danger { color: var(--danger-color); }
.text-warning { color: var(--warning-color); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }

/* 密码强度指示器 */
.password-strength {
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.strength-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.strength-bar {
    flex: 1;
    height: 4px;
    background-color: var(--border-light);
    border-radius: 2px;
    overflow: hidden;
}

.strength-fill {
    height: 100%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.strength-text {
    font-size: 0.875rem;
    font-weight: 600;
    min-width: 2rem;
}

/* 安全提示样式 */
.security-tips {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.tip-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.tip-item i {
    color: var(--success-color);
    font-size: 1rem;
}

/* 登录历史样式 */
.login-history {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--background-secondary);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--border-light);
}

.history-time {
    font-size: 0.875rem;
    color: var(--text-color);
    font-weight: 500;
}

.history-ip {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-family: monospace;
}

.history-status {
    font-size: 0.875rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-sm);
}

.history-status.success {
    color: var(--success-color);
    background-color: rgba(34, 197, 94, 0.1);
}

.history-status.failed {
    color: var(--error-color);
    background-color: rgba(239, 68, 68, 0.1);
}
