/* 响应式设计样式 */

/* 平板设备 (768px - 1024px) */
@media (max-width: 1024px) {
    .app-container {
        grid-template-columns: 200px 1fr;
    }
    
    .app-header {
        padding: 0.75rem 1.5rem;
    }
    
    .header-brand h1 {
        font-size: 1.25rem;
    }
    
    .main-content {
        padding: 1.5rem;
    }
}

/* 移动设备 (最大768px) */
@media (max-width: 768px) {
    .app-container {
        grid-template-columns: 1fr;
        position: relative;
    }
    
    .app-sidebar {
        position: fixed;
        left: -250px;
        top: 0;
        width: 250px;
        height: 100vh;
        transition: left 0.3s ease;
        z-index: 1500;
    }
    
    .app-sidebar.active {
        left: 0;
    }
    
    .app-header {
        padding: 0.75rem 1rem;
        position: relative;
    }
    
    .header-brand {
        gap: 0.5rem;
    }
    
    .header-brand h1 {
        font-size: 1.1rem;
    }
    
    .header-user {
        gap: 0.5rem;
    }
    
    /* 显示移动端菜单按钮 */
    .mobile-menu-toggle {
        display: block;
    }
    
    .main-content {
        padding: 1rem;
        margin: 0.5rem;
    }
    
    .breadcrumb {
        padding: 0.75rem 1rem;
    }
    
    .status-bar {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }
    
    /* 模态框适配 */
    .modal-content {
        width: 95%;
        margin: 1rem;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 1rem;
    }
    
    /* 表单适配 */
    .form-row {
        margin: 0 -0.5rem;
    }

    .form-col {
        padding: 0 0.5rem;
    }

    .form-control {
        padding: 0.6rem;
    }

    /* 表单网格在平板上堆叠 */
    .form-col {
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 1rem;
    }

    .form-col:last-child {
        margin-bottom: 0;
    }

    /* 系统状态网格适配 */
    .system-status {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 0.75rem;
    }
    
    /* 按钮适配 */
    .btn {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }
    
    /* 表格适配 */
    .table {
        font-size: 0.85rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem;
    }
    
    /* 通知适配 */
    .notification {
        top: 10px;
        right: 10px;
        left: 10px;
        transform: translateY(-100%);
    }
    
    .notification.show {
        transform: translateY(0);
    }
    
    .notification-content {
        min-width: auto;
        padding: 0.75rem 1rem;
    }
}

/* 小屏幕移动设备 (最大480px) */
@media (max-width: 480px) {
    .app-header {
        padding: 0.5rem;
    }
    
    .header-brand h1 {
        font-size: 1rem;
    }
    
    .header-brand .logo {
        width: 24px;
        height: 24px;
    }
    
    .main-content {
        padding: 0.75rem;
        margin: 0.25rem;
    }
    
    .breadcrumb {
        padding: 0.5rem;
        font-size: 0.8rem;
    }
    
    /* 导航菜单适配 */
    .nav-link {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }
    
    .nav-submenu .nav-link {
        padding-left: 2rem;
        font-size: 0.8rem;
    }
    
    /* 表单适配 */
    .form-group {
        margin-bottom: 1rem;
    }

    .form-row {
        margin: 0 -0.25rem;
        margin-bottom: 1rem;
    }

    .form-col {
        padding: 0 0.25rem;
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 0.75rem;
    }

    .form-col:last-child {
        margin-bottom: 0;
    }

    .form-control {
        padding: 0.5rem;
        font-size: 0.85rem;
    }

    .form-label {
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
    }

    .form-help {
        font-size: 0.75rem;
    }

    .form-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .form-actions .btn {
        width: 100%;
    }

    /* 系统状态网格适配 */
    .system-status {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .status-card {
        padding: 0.75rem;
    }

    .status-card .status-value {
        font-size: 1.25rem;
    }
    
    /* 按钮适配 */
    .btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.85rem;
    }
    
    /* 卡片适配 */
    .card-header,
    .card-body,
    .card-footer {
        padding: 0.75rem;
    }
    
    /* 模态框适配 */
    .modal-content {
        width: 98%;
        margin: 0.5rem;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 0.75rem;
    }
    
    .modal-header h3 {
        font-size: 1.1rem;
    }
}

/* 超大屏幕 (最小1200px) */
@media (min-width: 1200px) {
    .app-container {
        grid-template-columns: 280px 1fr;
    }
    
    .main-content {
        padding: 2.5rem;
    }
    
    .nav-link {
        padding: 0.875rem 2rem;
    }
    
    .nav-submenu .nav-link {
        padding-left: 3.5rem;
    }
}

/* 打印样式 */
@media print {
    .app-header,
    .app-sidebar,
    .status-bar,
    .modal,
    .notification {
        display: none !important;
    }
    
    .app-container {
        grid-template-columns: 1fr;
    }
    
    .main-content {
        padding: 0;
        margin: 0;
        box-shadow: none;
        border-radius: 0;
    }
    
    .breadcrumb {
        border-bottom: 2px solid #000;
        font-weight: bold;
    }
    
    .btn {
        display: none;
    }
    
    .form-control {
        border: 1px solid #000;
        background: transparent;
    }
    
    .table {
        border-collapse: collapse;
    }
    
    .table th,
    .table td {
        border: 1px solid #000;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000;
        --text-color: #000;
        --background-color: #fff;
    }
    
    .btn {
        border-width: 2px;
    }
    
    .form-control {
        border-width: 2px;
    }
    
    .nav-link:hover,
    .nav-link.active {
        background-color: #000;
        color: #fff;
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .spinner {
        animation: none;
    }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --background-color: #1a1a1a;
        --text-color: #e0e0e0;
        --white: #2d2d2d;
        --light-color: #3a3a3a;
        --border-color: #555;
    }
    
    .app-header {
        background: #1a4a4a;
    }
    
    .app-sidebar {
        background: #4a1a1a;
    }
    
    .form-control {
        background-color: var(--white);
        color: var(--text-color);
    }
    
    .table th {
        background-color: var(--light-color);
    }
}
