/**
 * @file main.c
 * @brief WebCfg模块化系统主程序 - 基于libmicrohttpd的HTTP服务器
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#include <stdio.h>
#include <stdlib.h>
#include <signal.h>
#include <unistd.h>
#include <string.h>
#include <getopt.h>
#include <sys/stat.h>
#include "config_interface.h"
#include "global_config.h"
#include "board_config.h"
#include "center_config.h"
#include "gateway_config.h"
#include "recorder_config.h"
#include "sci_config.h"
#include "switch_config.h"
#include "board_ntp.h"
#include "system_management.h"
#include "auth_handler.h"
#include "api_router.h"

// 全局变量
static volatile int g_server_running = 1;

// 配置结构体
typedef struct {
    int port;
    char web_root[512];
    char config_file[512];
    int max_connections;
    int timeout_seconds;
    int daemon_mode;
    int verbose;
} app_config_t;

// 默认配置
static app_config_t g_config = {
    .port = 8080,
    .web_root = "./web",
    .config_file = "",
    .max_connections = 10,
    .timeout_seconds = 20,
    .daemon_mode = 0,
    .verbose = 0
};

/**
 * @brief 显示版本信息
 */
static void show_version(void) {
    printf("WebCfg模块化系统 v%s\n", WEBCFG_VERSION);
    printf("构建平台: %s\n", TARGET_PLATFORM);
    printf("基于libmicrohttpd的HTTP服务器\n");
    printf("Copyright (c) 2024 Jimmy Deng\n");
}

/**
 * @brief 显示帮助信息
 */
static void show_help(const char *prog_name) {
    printf("用法: %s [选项]\n\n", prog_name);
    printf("WebCfg模块化系统 - 嵌入式设备配置管理服务器\n\n");
    
    printf("选项:\n");
    printf("  -p, --port=PORT          指定HTTP服务器端口 (默认: 8080)\n");
    printf("  -w, --web-root=DIR       指定Web静态文件根目录 (默认: ./web)\n");
    printf("  -c, --config=FILE        指定配置文件路径\n");
    printf("  -m, --max-conn=NUM       最大并发连接数 (默认: 100)\n");
    printf("  -t, --timeout=SEC        连接超时时间秒数 (默认: 30)\n");
    printf("  -d, --daemon             以守护进程模式运行\n");
    printf("  -v, --verbose            详细输出模式\n");
    printf("  -h, --help               显示此帮助信息\n");
    printf("  -V, --version            显示版本信息\n\n");
    
    printf("示例:\n");
    printf("  %s -p 80 -w /var/www/webcfg\n", prog_name);
    printf("  %s --port=8080 --web-root=./web --verbose\n", prog_name);
    printf("  %s -d -c /etc/webcfg/webcfg.conf\n", prog_name);
    printf("\n");
    
    printf("配置模块:\n");
    printf("  - 网络配置 (以太网/3G网络选择)\n");
    printf("  - 呼叫中心设备配置\n");
    printf("  - 网关功能配置\n");
    printf("  - 录音基站配置\n");
    printf("  - SCI基站功能配置\n");
    printf("  - 交换功能配置\n");
    printf("  - NTP时间同步配置\n");
    printf("  - 系统管理 (重启/复位/日志)\n");
    printf("  - 认证管理 (密码修改)\n\n");
    
    printf("API接口:\n");
    printf("  RESTful API: http://localhost:%d/api/v1/\n", g_config.port);
    printf("  Web管理界面: http://localhost:%d/\n", g_config.port);
    printf("\n");
}

/**
 * @brief 解析命令行参数
 */
static int parse_arguments(int argc, char *argv[]) {
    int opt;
    static struct option long_options[] = {
        {"port",        required_argument, 0, 'p'},
        {"web-root",    required_argument, 0, 'w'},
        {"config",      required_argument, 0, 'c'},
        {"max-conn",    required_argument, 0, 'm'},
        {"timeout",     required_argument, 0, 't'},
        {"daemon",      no_argument,       0, 'd'},
        {"verbose",     no_argument,       0, 'v'},
        {"help",        no_argument,       0, 'h'},
        {"version",     no_argument,       0, 'V'},
        {0, 0, 0, 0}
    };

    while ((opt = getopt_long(argc, argv, "p:w:c:m:t:dvhV", long_options, NULL)) != -1) {
        switch (opt) {
            case 'p':
                g_config.port = atoi(optarg);
                if (g_config.port <= 0 || g_config.port > 65535) {
                    fprintf(stderr, "错误: 端口号必须在1-65535范围内\n");
                    return -1;
                }
                break;
                
            case 'w':
                strncpy(g_config.web_root, optarg, sizeof(g_config.web_root) - 1);
                g_config.web_root[sizeof(g_config.web_root) - 1] = '\0';
                break;
                
            case 'c':
                strncpy(g_config.config_file, optarg, sizeof(g_config.config_file) - 1);
                g_config.config_file[sizeof(g_config.config_file) - 1] = '\0';
                break;
                
            case 'm':
                g_config.max_connections = atoi(optarg);
                if ((g_config.max_connections <= 0) || (g_config.max_connections > 10)) {
                    fprintf(stderr, "错误: 最大连接数必须在1-10之间\n");
                    return -1;
                }
                break;
                
            case 't':
                g_config.timeout_seconds = atoi(optarg);
                if (g_config.timeout_seconds <= 0) {
                    fprintf(stderr, "错误: 超时时间必须大于0\n");
                    return -1;
                }
                break;
                
            case 'd':
                g_config.daemon_mode = 1;
                break;
                
            case 'v':
                g_config.verbose = 1;
                break;
                
            case 'h':
                show_help(argv[0]);
                exit(0);
                
            case 'V':
                show_version();
                exit(0);
                
            case '?':
                fprintf(stderr, "使用 '%s --help' 查看帮助信息\n", argv[0]);
                return -1;
                
            default:
                return -1;
        }
    }

    // 检查多余参数
    if (optind < argc) {
        fprintf(stderr, "错误: 未知参数 '");
        while (optind < argc) {
            fprintf(stderr, "%s ", argv[optind++]);
        }
        fprintf(stderr, "'\n");
        fprintf(stderr, "使用 '%s --help' 查看帮助信息\n", argv[0]);
        return -1;
    }

    return 0;
}

/**
 * @brief 验证配置参数
 */
static int validate_config(void) {
    struct stat st;
    
    // 检查Web根目录是否存在
    if (stat(g_config.web_root, &st) != 0 || !S_ISDIR(st.st_mode)) {
        fprintf(stderr, "错误: Web根目录不存在或不是目录: %s\n", g_config.web_root);
        return -1;
    }
    
    // 检查配置文件（如果指定）
    if (strlen(g_config.config_file) > 0) {
        if (stat(g_config.config_file, &st) != 0 || !S_ISREG(st.st_mode)) {
            fprintf(stderr, "错误: 配置文件不存在或不是普通文件: %s\n", g_config.config_file);
            return -1;
        }
    }
    
    return 0;
}

/**
 * @brief 成为守护进程
 */
static int daemonize(void) {
    pid_t pid = fork();
    
    if (pid < 0) {
        perror("fork失败");
        return -1;
    }
    
    if (pid > 0) {
        // 父进程退出
        exit(0);
    }
    
    // 子进程继续
    if (setsid() < 0) {
        perror("setsid失败");
        return -1;
    }
    
    // 改变工作目录到根目录
    if (chdir("/") < 0) {
        perror("chdir失败");
        return -1;
    }
    
    // 关闭标准文件描述符
    close(STDIN_FILENO);
    close(STDOUT_FILENO);
    close(STDERR_FILENO);
    
    return 0;
}

/**
 * @brief 信号处理函数
 */
static void signal_handler(int sig) {
    (void)sig; // 避免未使用参数警告
    
    if (g_config.verbose && !g_config.daemon_mode) {
        printf("\n接收到停止信号，正在关闭服务器...\n");
    }
    g_server_running = 0;
}

/**
 * @brief 注册API路由 - 按照设计方案注册网络配置API
 */
static int register_api_routes(void) {
    // 注册网络配置API路由 - 按照设计方案的API接口
    if (api_router_register("/api/v1/config/spec/netcs", HTTP_METHOD_GET,
                           handle_spec_netselection_get, "获取网络选择配置") != 0) {
        return -1;
    }

    if (api_router_register("/api/v1/config/spec/netcs", HTTP_METHOD_POST,
                           handle_spec_netselection_post, "设置网络选择配置") != 0) {
        return -1;
    }

    if (api_router_register("/api/v1/config/board/ethernet", HTTP_METHOD_GET,
                           handle_board_ethernet_get, "获取以太网配置") != 0) {
        return -1;
    }

    if (api_router_register("/api/v1/config/board/ethernet", HTTP_METHOD_POST,
                           handle_board_ethernet_post, "设置以太网配置") != 0) {
        return -1;
    }

    // 注册呼叫中心配置API路由 - 按照设计方案对应0center.c
    if (api_router_register("/api/v1/config/center", HTTP_METHOD_GET,
                           handle_center_get, "获取呼叫中心配置") != 0) {
        return -1;
    }

    if (api_router_register("/api/v1/config/center", HTTP_METHOD_POST,
                           handle_center_post, "设置呼叫中心配置") != 0) {
        return -1;
    }

    // 注册网关配置API路由 - 按照设计方案对应0gateway.c
    if (api_router_register("/api/v1/config/gateway", HTTP_METHOD_GET,
                           handle_gateway_get, "获取网关配置") != 0) {
        return -1;
    }

    if (api_router_register("/api/v1/config/gateway", HTTP_METHOD_POST,
                           handle_gateway_post, "设置网关配置") != 0) {
        return -1;
    }

    // 注册录音配置API路由 - 按照设计方案对应0recorder.c
    if (api_router_register("/api/v1/config/recorder", HTTP_METHOD_GET,
                           handle_recorder_get, "获取录音配置") != 0) {
        return -1;
    }

    if (api_router_register("/api/v1/config/recorder", HTTP_METHOD_POST,
                           handle_recorder_post, "设置录音配置") != 0) {
        return -1;
    }

    // 注册SCI基站配置API路由 - 按照设计方案对应0sci.c
    if (api_router_register("/api/v1/config/sci", HTTP_METHOD_GET,
                           handle_sci_get, "获取SCI基站配置") != 0) {
        return -1;
    }

    if (api_router_register("/api/v1/config/sci", HTTP_METHOD_POST,
                           handle_sci_post, "设置SCI基站配置") != 0) {
        return -1;
    }

    // 注册交换机配置API路由 - 按照设计方案对应0switch.c
    if (api_router_register("/api/v1/config/switch", HTTP_METHOD_GET,
                           handle_switch_get, "获取交换机配置") != 0) {
        return -1;
    }

    if (api_router_register("/api/v1/config/switch", HTTP_METHOD_POST,
                           handle_switch_post, "设置交换机配置") != 0) {
        return -1;
    }

    // 注册NTP配置API路由 - 按照设计方案对应0ntp.c
    if (api_router_register("/api/v1/config/board/ntp", HTTP_METHOD_GET,
                           handle_board_ntp_get, "获取NTP配置") != 0) {
        return -1;
    }

    if (api_router_register("/api/v1/config/board/ntp", HTTP_METHOD_POST,
                           handle_board_ntp_post, "设置NTP配置") != 0) {
        return -1;
    }

    // 注册系统管理API路由
    if (api_router_register("/api/v1/system/reboot", HTTP_METHOD_POST, handle_system_reboot, "System reboot") != 0) {
        printf("Failed to register system reboot API\n");
        return -1;
    }

    if (api_router_register("/api/v1/system/reset", HTTP_METHOD_POST, handle_system_reset, "System reset") != 0) {
        printf("Failed to register system reset API\n");
        return -1;
    }

    if (api_router_register("/api/v1/system/logs", HTTP_METHOD_GET, handle_system_logs, "System logs") != 0) {
        printf("Failed to register system logs API\n");
        return -1;
    }

    if (api_router_register("/api/v1/system/signal", HTTP_METHOD_GET, handle_system_signal, "3G signal strength") != 0) {
        printf("Failed to register system signal API\n");
        return -1;
    }

    if (api_router_register("/api/v1/system/upload", HTTP_METHOD_POST, handle_system_upload, "File upload") != 0) {
        printf("Failed to register system upload API\n");
        return -1;
    }

    // 注册认证API路由 - 按照设计方案对应0passwd.c
    if (api_router_register("/api/v1/auth/password", HTTP_METHOD_POST,
                           handle_auth_password, "修改管理员密码") != 0) {
        printf("Failed to register auth password API\n");
        return -1;
    }

    // 注册登录API路由
    if (api_router_register("/api/v1/auth/login", HTTP_METHOD_POST,
                           handle_auth_login, "用户登录") != 0) {
        printf("Failed to register auth login API\n");
        return -1;
    }

    // 注册退出API路由
    if (api_router_register("/api/v1/auth/logout", HTTP_METHOD_POST,
                           handle_auth_logout, "用户退出") != 0) {
        printf("Failed to register auth logout API\n");
        return -1;
    }

    printf("API routes registered successfully\n");
    return 0;
}

/**
 * @brief 主函数 - libmicrohttpd HTTP服务器模式
 */
int main(int argc, char *argv[]) {
    // 解析命令行参数
    if (parse_arguments(argc, argv) != 0) {
        return -1;
    }

    // 验证配置参数
    if (validate_config() != 0) {
        return -1;
    }

    // 成为守护进程（如果需要）
    if (g_config.daemon_mode) {
        if (daemonize() != 0) {
            fprintf(stderr, "Failed to daemonize process\n");
            return -1;
        }
        printf("WebCfg Modular System v1.0 - libmicrohttpd HTTP Server (Daemon Mode)\n");
    } else {
        printf("WebCfg Modular System v1.0 - libmicrohttpd HTTP Server\n");
    }

    printf("Starting on port %d...\n", g_config.port);

    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 初始化全局配置
    if (global_config_init(g_config.config_file) != 0) {
        printf("Failed to initialize global config\n");
        return -1;
    }

    // 初始化配置管理器
    if (config_manager_init() != 0) {
        printf("Failed to initialize config manager\n");
        return -1;
    }

    // 初始化通用板卡配置模块
    if (common_board_config_init() != 0) {
        printf("Failed to initialize common board config\n");
        config_manager_cleanup();
        return -1;
    }

    // 初始化网络配置模块
    if (network_config_init() != 0) {
        printf("Failed to initialize network config\n");
        config_manager_cleanup();
        return -1;
    }

    // 初始化呼叫中心配置模块
    if (center_config_init() != 0) {
        printf("Failed to initialize center config\n");
        network_config_cleanup();
        config_manager_cleanup();
        return -1;
    }

    // 初始化网关配置模块
    if (gateway_config_init() != 0) {
        printf("Failed to initialize gateway config\n");
        gateway_config_cleanup();
        center_config_cleanup();
        network_config_cleanup();
        config_manager_cleanup();
        return -1;
    }

    // 初始化录音配置模块
    if (recorder_config_init() != 0) {
        printf("Failed to initialize recorder config\n");
        gateway_config_cleanup();
        center_config_cleanup();
        network_config_cleanup();
        config_manager_cleanup();
        return -1;
    }

    // 初始化SCI配置模块
    if (sci_config_init() != 0) {
        printf("Failed to initialize SCI config\n");
        recorder_config_cleanup();
        gateway_config_cleanup();
        center_config_cleanup();
        network_config_cleanup();
        config_manager_cleanup();
        return -1;
    }

    // 初始化交换机配置模块
    if (switch_config_init() != 0) {
        printf("Failed to initialize switch config\n");
        sci_config_cleanup();
        recorder_config_cleanup();
        gateway_config_cleanup();
        center_config_cleanup();
        network_config_cleanup();
        config_manager_cleanup();
        return -1;
    }

    // 初始化NTP配置模块
    if (board_ntp_init() != 0) {
        printf("Failed to initialize NTP config\n");
        switch_config_cleanup();
        sci_config_cleanup();
        recorder_config_cleanup();
        gateway_config_cleanup();
        center_config_cleanup();
        network_config_cleanup();
        config_manager_cleanup();
        return -1;
    }

    // 初始化系统管理模块
    if (system_management_init() != 0) {
        printf("Failed to initialize system management\n");
        system_management_cleanup();
        board_ntp_cleanup();
        switch_config_cleanup();
        sci_config_cleanup();
        recorder_config_cleanup();
        gateway_config_cleanup();
        center_config_cleanup();
        network_config_cleanup();
        config_manager_cleanup();
        return -1;
    }

    // 初始化API路由系统
    if (api_router_init() != 0) {
        printf("Failed to initialize API router\n");
        system_management_cleanup();
        board_ntp_cleanup();
        switch_config_cleanup();
        sci_config_cleanup();
        recorder_config_cleanup();
        gateway_config_cleanup();
        center_config_cleanup();
        network_config_cleanup();
        config_manager_cleanup();
        return -1;
    }

    // 注册API路由
    if (register_api_routes() != 0) {
        printf("Failed to register API routes\n");
        api_router_cleanup();
        system_management_cleanup();
        board_ntp_cleanup();
        switch_config_cleanup();
        sci_config_cleanup();
        recorder_config_cleanup();
        gateway_config_cleanup();
        center_config_cleanup();
        network_config_cleanup();
        config_manager_cleanup();
        return -1;
    }

    // 配置HTTP服务器
    http_server_config_t server_config = {
        .port = g_config.port,
        .max_connections = g_config.max_connections,
        .timeout_seconds = g_config.timeout_seconds,
        .document_root = g_config.web_root
    };

    // 启动HTTP服务器
    if (http_server_start(&server_config) != 0) {
        printf("Failed to start HTTP server\n");
        api_router_cleanup();
        system_management_cleanup();
        board_ntp_cleanup();
        switch_config_cleanup();
        sci_config_cleanup();
        recorder_config_cleanup();
        gateway_config_cleanup();
        center_config_cleanup();
        network_config_cleanup();
        config_manager_cleanup();
        return -1;
    }

    if (g_config.verbose) {
        printf("System initialized successfully.\n");
        printf("Registered modules: %d\n", config_manager_get_module_count());
        printf("Configuration:\n");
        printf("  Port: %d\n", g_config.port);
        printf("  Web Root: %s\n", g_config.web_root);
        printf("  Max Connections: %d\n", g_config.max_connections);
        printf("  Timeout: %d seconds\n", g_config.timeout_seconds);
        if (strlen(g_config.config_file) > 0) {
            printf("  Config File: %s\n", g_config.config_file);
        }
    }
    
    printf("HTTP server running on http://localhost:%d\n", g_config.port);
    if (!g_config.daemon_mode) {
        printf("Press Ctrl+C to stop the server\n");
    }

    // 主循环
    while (g_server_running) {
        sleep(1);
    }

    // 清理资源
    if (g_config.verbose && !g_config.daemon_mode) {
        printf("Shutting down...\n");
    }
    http_server_stop();
    api_router_cleanup();
    system_management_cleanup();
    board_ntp_cleanup();
    switch_config_cleanup();
    sci_config_cleanup();
    recorder_config_cleanup();
    gateway_config_cleanup();
    center_config_cleanup();
    network_config_cleanup();
    config_manager_cleanup();
    global_config_cleanup();

    if (g_config.verbose && !g_config.daemon_mode) {
        printf("Server stopped successfully\n");
    }
    return 0;
}