/**
 * @file gateway_config.c
 * @brief 网关配置管理模块实现 - 对应旧项目0gateway.c
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#include "gateway_config.h"
#include "config_interface.h"
#include "global_config.h"
#include "file_utils.h"
#include "network_utils.h"
#include "api_router.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <arpa/inet.h>

// 静态变量（按命名规范）
static config_module_t s_gateway_module;

/**
 * @brief 设置默认配置
 */
void gateway_config_set_default(cfg_gateway_t *config) {
    if (!config) return;
    
    memset(config, 0, sizeof(cfg_gateway_t));
    
    // 严格对应旧项目默认值
    config->center_no = MAKEADDR(GATEWAY_DEFAULT_DS, GATEWAY_DEFAULT_SW, GATEWAY_DEFAULT_BS, 28);
    config->center_outssi = GATEWAY_DEFAULT_OUTSSI;             // 旧项目默认值
    config->center_inssi = GATEWAY_DEFAULT_INSSI;               // 旧项目默认值
    config->vchan_sum = GATEWAY_DEFAULT_VCHAN_SUM;              // 默认通道数
    config->center_voice_port = GATEWAY_DEFAULT_VOICE_PORT;     // 语音端口
    config->listen_agent_port = GATEWAY_DEFAULT_AGENT_PORT;     // 代理端口
    config->peer_net_type = 0;                                  // 单播模式
    config->send_all_agent_ip = inet_addr(GATEWAY_DEFAULT_AGENT_IP);
    config->send_to_agent_port = GATEWAY_DEFAULT_AGENT_SEND_PORT;
    config->inssi_num = GATEWAY_DEFAULT_INSSI_NUM;              // 默认号码个数
    config->spec_function = 0;                                  // 特殊功能关闭
}

/**
 * @brief 加载网关配置
 */
int gateway_config_load(cfg_gateway_t *config) {
    if (!config) return -1;
    
    // 获取动态配置路径
    char config_path[512];
    if (global_config_get_path(GATEWAY_CONFIG_FILE, config_path, sizeof(config_path)) != 0) {
        printf("Warning: Failed to get gateway config path, using defaults\n");
        gateway_config_set_default(config);
        return 0;
    }
    
    // 使用公共工具模块读取二进制配置文件
    if (file_utils_read_binary(config_path, 0, sizeof(cfg_gateway_t), config) != 0) {
        printf("Warning: Failed to read gateway config from %s, using defaults\n", config_path);
        gateway_config_set_default(config);
        return 0; // 使用默认配置不算错误
    }
    
    return 0;
}

/**
 * @brief 保存网关配置
 */
int gateway_config_save(const cfg_gateway_t *config) {
    if (!config) return -1;
    
    // 验证配置
    if (gateway_config_validate(config) != 0) {
        return -1;
    }
    
    // 获取动态配置路径
    char config_path[512];
    if (global_config_get_path(GATEWAY_CONFIG_FILE, config_path, sizeof(config_path)) != 0) {
        printf("Error: Failed to get gateway config path for saving\n");
        return -1;
    }
    
    // 使用公共工具模块写入二进制配置文件
    return file_utils_write_binary(config_path, 0, sizeof(cfg_gateway_t), config);
}

/**
 * @brief 验证网关配置
 */
int gateway_config_validate(const cfg_gateway_t *config) {
    if (!config) return -1;
    
    // 验证DS-SW-BS地址编码范围
    uint32_t ds = (config->center_no >> 16) & 0x3F;
    uint32_t sw = (config->center_no >> 8) & 0xFF;
    uint32_t bs = config->center_no & 0x1F;
    
    if (ds < 1 || ds > 63) {
        return -1;
    }
    
    if (sw < 1 || sw > 255) {
        return -1;
    }
    
    if (bs < 1 || bs > 31) {
        return -1;
    }
    
    // 验证端口范围（uint16_t 已经限制在 0-65535，只需检查是否为0）
    if (config->center_voice_port == 0) {
        return -1;
    }
    
    if (config->listen_agent_port == 0) {
        return -1;
    }
    
    if (config->send_to_agent_port == 0) {
        return -1;
    }
    
    // 验证语音通道数
    if (config->vchan_sum < 1 || config->vchan_sum > 64) {
        return -1;
    }
    
    // 验证对等网络类型
    if (config->peer_net_type > 2) {
        return -1;
    }
    
    // 验证呼入号码数量
    if (config->inssi_num > 200) {
        return -1;
    }
    
    return 0;
}

/**
 * @brief 配置结构转JSON
 */
int gateway_config_to_json(const cfg_gateway_t *config, cJSON **json) {
    if (!config || !json) return -1;
    
    *json = cJSON_CreateObject();
    if (!*json) return -1;
    
    // 输出完整的11个核心参数
    cJSON_AddNumberToObject(*json, "center_no", config->center_no);
    
    // 分解地址编码便于前端显示
    cJSON_AddNumberToObject(*json, "center_ds", (config->center_no >> 16) & 0x3F);
    cJSON_AddNumberToObject(*json, "center_sw", (config->center_no >> 8) & 0xFF);
    cJSON_AddNumberToObject(*json, "center_bs", config->center_no & 0x1F);
    
    // SSI号码以十六进制字符串形式输出
    char ssi_str[16];
    snprintf(ssi_str, sizeof(ssi_str), "%X", config->center_outssi);
    cJSON_AddStringToObject(*json, "center_outssi", ssi_str);
    
    snprintf(ssi_str, sizeof(ssi_str), "%X", config->center_inssi);
    cJSON_AddStringToObject(*json, "center_inssi", ssi_str);
    
    cJSON_AddNumberToObject(*json, "vchan_sum", config->vchan_sum);
    cJSON_AddNumberToObject(*json, "center_voice_port", config->center_voice_port);
    cJSON_AddNumberToObject(*json, "listen_agent_port", config->listen_agent_port);
    cJSON_AddNumberToObject(*json, "peer_net_type", config->peer_net_type);
    
    // IP地址转换
    char ip_str[16];
    if (ip_utils_binary_to_string(config->send_all_agent_ip, ip_str, sizeof(ip_str)) == 0) {
        cJSON_AddStringToObject(*json, "send_all_agent_ip", ip_str);
    } else {
        cJSON_AddStringToObject(*json, "send_all_agent_ip", "*********");
    }
    
    cJSON_AddNumberToObject(*json, "send_to_agent_port", config->send_to_agent_port);
    cJSON_AddNumberToObject(*json, "inssi_num", config->inssi_num);
    cJSON_AddNumberToObject(*json, "spec_function", config->spec_function);
    
    return 0;
}

/**
 * @brief JSON转配置结构
 */
int gateway_json_to_config(const cJSON *json, cfg_gateway_t *config) {
    if (!json || !config) return -1;
    
    // 先设置默认值
    gateway_config_set_default(config);
    
    // 解析JSON字段
    cJSON *item;
    
    // 解析地址编码
    uint32_t ds = GATEWAY_DEFAULT_DS;
    uint32_t sw = GATEWAY_DEFAULT_SW;
    uint32_t bs = GATEWAY_DEFAULT_BS;
    
    item = cJSON_GetObjectItem(json, "center_ds");
    if (item && cJSON_IsNumber(item)) {
        ds = (uint32_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "center_sw");
    if (item && cJSON_IsNumber(item)) {
        sw = (uint32_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "center_bs");
    if (item && cJSON_IsNumber(item)) {
        bs = (uint32_t)item->valueint;
    }
    
    // 构建地址编码
    config->center_no = MAKEADDR(ds, sw, bs, 28);
    
    // 解析SSI号码（十六进制字符串）
    item = cJSON_GetObjectItem(json, "center_outssi");
    if (item && cJSON_IsString(item)) {
        config->center_outssi = strtoul(item->valuestring, NULL, 16);
    }
    
    item = cJSON_GetObjectItem(json, "center_inssi");
    if (item && cJSON_IsString(item)) {
        config->center_inssi = strtoul(item->valuestring, NULL, 16);
    }
    
    item = cJSON_GetObjectItem(json, "vchan_sum");
    if (item && cJSON_IsNumber(item)) {
        config->vchan_sum = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "center_voice_port");
    if (item && cJSON_IsNumber(item)) {
        config->center_voice_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "listen_agent_port");
    if (item && cJSON_IsNumber(item)) {
        config->listen_agent_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "peer_net_type");
    if (item && cJSON_IsNumber(item)) {
        config->peer_net_type = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "send_all_agent_ip");
    if (item && cJSON_IsString(item)) {
        uint32_t ip_temp;
        if (ip_utils_string_to_binary(item->valuestring, &ip_temp) != 0) {
            return -1;
        }
        config->send_all_agent_ip = ip_temp;
    }
    
    item = cJSON_GetObjectItem(json, "send_to_agent_port");
    if (item && cJSON_IsNumber(item)) {
        config->send_to_agent_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "inssi_num");
    if (item && cJSON_IsNumber(item)) {
        config->inssi_num = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "spec_function");
    if (item && cJSON_IsNumber(item)) {
        config->spec_function = (uint16_t)item->valueint;
    }
    
    return 0;
}

/**
 * @brief 获取网关配置信息（API处理函数）
 */
int handle_gateway_get(struct MHD_Connection *connection, 
                      const char *url, 
                      const char *method,
                      const char *upload_data, 
                      size_t *upload_data_size,
                      void **con_cls) {
    (void)url; (void)method; (void)upload_data; (void)upload_data_size; (void)con_cls;
    
    cfg_gateway_t config;
    cJSON *json = NULL;
    
    // 加载配置
    if (gateway_config_load(&config) != 0) {
        api_response_t *response = api_response_create_error(500, "Failed to load gateway config");
        api_response_send(connection, response);
        api_response_free(response);
        return -1;
    }

    // 转换为JSON
    if (gateway_config_to_json(&config, &json) != 0) {
        api_response_t *response = api_response_create_error(500, "Failed to convert config to JSON");
        api_response_send(connection, response);
        api_response_free(response);
        return -1;
    }

    // 发送响应
    api_response_t *response = api_response_create_success(json);
    api_response_send(connection, response);
    api_response_free(response);
    cJSON_Delete(json);

    return 0;
}

/**
 * @brief 设置网关配置信息（API处理函数）
 */
int handle_gateway_post(struct MHD_Connection *connection, 
                       const char *url, 
                       const char *method,
                       const char *upload_data, 
                       size_t *upload_data_size,
                       void **con_cls) {
    (void)url; (void)method;
    
    // 获取POST数据
    cJSON *json = api_request_get_json_data(connection, upload_data, upload_data_size, con_cls);
    if (!json) {
        api_response_t *response = api_response_create_error(400, "Invalid JSON data");
        api_response_send(connection, response);
        api_response_free(response);
        return -1;
    }

    cfg_gateway_t config;

    // JSON转配置结构
    if (gateway_json_to_config(json, &config) != 0) {
        cJSON_Delete(json);
        api_response_t *response = api_response_create_error(400, "Invalid config data");
        api_response_send(connection, response);
        api_response_free(response);
        return -1;
    }

    // 保存配置
    if (gateway_config_save(&config) != 0) {
        cJSON_Delete(json);
        api_response_t *response = api_response_create_error(500, "Failed to save config");
        api_response_send(connection, response);
        api_response_free(response);
        return -1;
    }

    cJSON_Delete(json);

    // 返回成功响应
    cJSON *success_json = cJSON_CreateObject();
    cJSON_AddStringToObject(success_json, "message", "Gateway config saved successfully");

    api_response_t *response = api_response_create_success(success_json);
    api_response_send(connection, response);
    api_response_free(response);
    cJSON_Delete(success_json);

    return 0;
}

// 配置模块接口实现（按命名规范）
static int gateway_module_load(void *config) {
    return gateway_config_load((cfg_gateway_t *)config);
}

static int gateway_module_save(const void *config) {
    return gateway_config_save((const cfg_gateway_t *)config);
}

static int gateway_module_validate(const void *config) {
    return gateway_config_validate((const cfg_gateway_t *)config);
}

static int gateway_module_to_json(const void *config, cJSON **json) {
    return gateway_config_to_json((const cfg_gateway_t *)config, json);
}

static int gateway_module_from_json(const cJSON *json, void *config) {
    return gateway_json_to_config(json, (cfg_gateway_t *)config);
}

/**
 * @brief 初始化网关配置模块
 */
int gateway_config_init(void) {
    // 初始化配置模块结构
    s_gateway_module.module_name = "gateway";
    s_gateway_module.config_file_path = GATEWAY_CONFIG_FILE;
    s_gateway_module.config_struct_size = sizeof(cfg_gateway_t);
    s_gateway_module.load_config = gateway_module_load;
    s_gateway_module.save_config = gateway_module_save;
    s_gateway_module.validate_config = gateway_module_validate;
    s_gateway_module.config_to_json = gateway_module_to_json;
    s_gateway_module.json_to_config = gateway_module_from_json;

    // 注册到配置管理器
    if (config_manager_register(&s_gateway_module) != 0) {
        printf("Failed to register gateway config module\n");
        return -1;
    }

    printf("Gateway config module initialized\n");
    return 0;
}

/**
 * @brief 清理网关配置模块
 */
void gateway_config_cleanup(void) {
    // 清理模块资源（如果有的话）
    printf("Gateway config module cleaned up\n");
}
