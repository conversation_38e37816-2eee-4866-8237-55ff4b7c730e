/**
 * @file sci_config.c
 * @brief SCI基站配置管理实现 - 对应旧项目0sci.c
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#include "sci_config.h"
#include "config_interface.h"
#include "global_config.h"
#include "file_utils.h"
#include "network_utils.h"
#include "api_router.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <arpa/inet.h>

// 静态变量（按命名规范）
static config_module_t s_sci_module;

/**
 * @brief 设置SCI配置默认值（严格对应旧项目）
 */
void sci_config_set_default(cfg_sci_t *config) {
    if (!config) return;

    memset(config, 0, sizeof(cfg_sci_t));
    
    // 对应旧项目set_default_value_sci()函数的默认值
    config->get_cfg_method = 0;                        // 默认自动获取
    config->network_mode = 0;                          // 默认点对点模式
    config->voice_ip = inet_addr(SCI_DEFAULT_VOICE_IP); // 默认语音IP
    config->data_listen_port = SCI_DEFAULT_DATA_PORT;   // 默认数据端口
    config->vbus_base_port = SCI_DEFAULT_VOICE_PORT;    // 默认语音端口
    config->net_address = (SCI_DEFAULT_NET_DS << 16) | (SCI_DEFAULT_NET_SW << 8) | SCI_DEFAULT_NET_BS; // DS=10, SW=1, BS=1
    config->base_type = SCI_DEFAULT_BASE_TYPE;          // 默认单工基站
    config->vcan_number = SCI_DEFAULT_VCHAN_NUM;        // 默认通道数
    config->buffertime = SCI_DEFAULT_BUFFER_TIME;       // 默认缓冲时间
    config->downtime = SCI_DEFAULT_DOWN_TIME;           // 默认掉线时间
    config->resettime = SCI_DEFAULT_RESET_TIME;         // 默认重置时间
    
    // 初始化通道映射
    for (int i = 0; i < 12; i++) {
        config->vbus_to_chan[i] = i + 1;
    }
}

/**
 * @brief 加载SCI配置
 */
int sci_config_load(cfg_sci_t *config) {
    if (!config) return -1;
    
    // 获取动态配置路径
    char config_path[512];
    if (global_config_get_path(SCI_CONFIG_FILE, config_path, sizeof(config_path)) != 0) {
        printf("Warning: Failed to get SCI config path, using defaults\n");
        sci_config_set_default(config);
        return 0;
    }
    
    // 使用公共工具模块读取二进制配置文件
    if (file_utils_read_binary(config_path, 0, sizeof(cfg_sci_t), config) != 0) {
        printf("Warning: Failed to read SCI config from %s, using defaults\n", config_path);
        sci_config_set_default(config);
        return 0; // 使用默认配置不算错误
    }
    
    return 0;
}

/**
 * @brief 保存SCI配置
 */
int sci_config_save(const cfg_sci_t *config) {
    if (!config) return -1;
    
    // 验证配置
    if (sci_config_validate(config) != 0) {
        return -1;
    }
    
    // 获取动态配置路径
    char config_path[512];
    if (global_config_get_path(SCI_CONFIG_FILE, config_path, sizeof(config_path)) != 0) {
        printf("Error: Failed to get SCI config path for saving\n");
        return -1;
    }
    
    // 使用公共工具模块写入二进制配置文件
    return file_utils_write_binary(config_path, 0, sizeof(cfg_sci_t), config);
}

/**
 * @brief 验证SCI配置（严格对应旧项目）
 */
int sci_config_validate(const cfg_sci_t *config) {
    if (!config) return -1;

    // 验证配置获取方式
    if (config->get_cfg_method > 1) {
        printf("Invalid config method: %d\n", config->get_cfg_method);
        return -1;
    }
    
    // 验证网络模式
    if (config->network_mode > 1) {
        printf("Invalid network mode: %d\n", config->network_mode);
        return -1;
    }
    
    // 验证基站类型
    if (config->base_type < 1 || config->base_type > 2) {
        printf("Invalid base type: %d\n", config->base_type);
        return -1;
    }
    
    // 验证地址编码范围
    uint32_t ds = (config->net_address >> 16) & 0x3F;
    uint32_t sw = (config->net_address >> 8) & 0xFF;
    uint32_t bs = config->net_address & 0x1F;
    
    if (ds < 1 || ds > 63 || sw < 1 || sw > 255 || bs < 1 || bs > 31) {
        printf("Invalid network address: DS=%u, SW=%u, BS=%u\n", ds, sw, bs);
        return -1;
    }
    
    // 验证语音通道数
    if (config->vcan_number < 1 || config->vcan_number > 64) {
        printf("Invalid voice channel count: %d\n", config->vcan_number);
        return -1;
    }
    
    // 验证端口范围
    if (config->data_listen_port == 0 || config->vbus_base_port == 0) {
        printf("Invalid port configuration\n");
        return -1;
    }

    return 0;
}

/**
 * @brief SCI配置转JSON（严格对应旧项目）
 */
int sci_config_to_json(const cfg_sci_t *config, cJSON **json) {
    if (!config || !json) return -1;

    *json = cJSON_CreateObject();
    if (!*json) return -1;

    // 输出完整的12个核心参数
    cJSON_AddNumberToObject(*json, "get_cfg_method", config->get_cfg_method);
    cJSON_AddNumberToObject(*json, "network_mode", config->network_mode);
    
    // IP地址转换
    char ip_str[16];
    if (ip_utils_binary_to_string(config->voice_ip, ip_str, sizeof(ip_str)) == 0) {
        cJSON_AddStringToObject(*json, "voice_ip", ip_str);
    }
    
    cJSON_AddNumberToObject(*json, "data_listen_port", config->data_listen_port);
    cJSON_AddNumberToObject(*json, "vbus_base_port", config->vbus_base_port);
    cJSON_AddNumberToObject(*json, "net_address", config->net_address);
    
    // 分解地址编码便于前端显示
    cJSON_AddNumberToObject(*json, "net_ds", (config->net_address >> 16) & 0x3F);
    cJSON_AddNumberToObject(*json, "net_sw", (config->net_address >> 8) & 0xFF);
    cJSON_AddNumberToObject(*json, "net_bs", config->net_address & 0x1F);
    
    cJSON_AddNumberToObject(*json, "base_type", config->base_type);
    
    // 通道映射数组
    cJSON *chan_array = cJSON_CreateArray();
    for (int i = 0; i < 12; i++) {
        cJSON_AddItemToArray(chan_array, cJSON_CreateNumber(config->vbus_to_chan[i]));
    }
    cJSON_AddItemToObject(*json, "vbus_to_chan", chan_array);
    
    cJSON_AddNumberToObject(*json, "vcan_number", config->vcan_number);
    cJSON_AddNumberToObject(*json, "buffertime", config->buffertime);
    cJSON_AddNumberToObject(*json, "downtime", config->downtime);
    cJSON_AddNumberToObject(*json, "resettime", config->resettime);

    return 0;
}

/**
 * @brief JSON转SCI配置（严格对应旧项目）
 */
int sci_json_to_config(const cJSON *json, cfg_sci_t *config) {
    if (!json || !config) return -1;

    // 先设置默认值
    sci_config_set_default(config);

    // 解析JSON字段
    cJSON *item;

    // 配置获取方式
    item = cJSON_GetObjectItem(json, "get_cfg_method");
    if (item && cJSON_IsNumber(item)) {
        config->get_cfg_method = item->valueint;
    }

    // 网络模式
    item = cJSON_GetObjectItem(json, "network_mode");
    if (item && cJSON_IsNumber(item)) {
        config->network_mode = item->valueint;
    }

    // 语音IP地址
    item = cJSON_GetObjectItem(json, "voice_ip");
    if (item && cJSON_IsString(item)) {
        if (ip_utils_string_to_binary(item->valuestring, &config->voice_ip) != 0) {
            return -1;
        }
    }

    // 数据监听端口
    item = cJSON_GetObjectItem(json, "data_listen_port");
    if (item && cJSON_IsNumber(item)) {
        config->data_listen_port = item->valueint;
    }

    // 语音总线端口
    item = cJSON_GetObjectItem(json, "vbus_base_port");
    if (item && cJSON_IsNumber(item)) {
        config->vbus_base_port = item->valueint;
    }

    // 处理地址编码（优先使用分解的DS/SW/BS）
    cJSON *ds_item = cJSON_GetObjectItem(json, "net_ds");
    cJSON *sw_item = cJSON_GetObjectItem(json, "net_sw");
    cJSON *bs_item = cJSON_GetObjectItem(json, "net_bs");
    
    if (ds_item && sw_item && bs_item && 
        cJSON_IsNumber(ds_item) && cJSON_IsNumber(sw_item) && cJSON_IsNumber(bs_item)) {
        uint32_t ds = ds_item->valueint;
        uint32_t sw = sw_item->valueint;
        uint32_t bs = bs_item->valueint;
        config->net_address = (ds << 16) | (sw << 8) | bs;
    } else {
        // 备用：直接使用net_address
        item = cJSON_GetObjectItem(json, "net_address");
        if (item && cJSON_IsNumber(item)) {
            config->net_address = item->valueint;
        }
    }

    // 基站类型
    item = cJSON_GetObjectItem(json, "base_type");
    if (item && cJSON_IsNumber(item)) {
        config->base_type = item->valueint;
    }

    // 通道映射数组
    item = cJSON_GetObjectItem(json, "vbus_to_chan");
    if (item && cJSON_IsArray(item)) {
        int array_size = cJSON_GetArraySize(item);
        for (int i = 0; i < 12 && i < array_size; i++) {
            cJSON *chan_item = cJSON_GetArrayItem(item, i);
            if (chan_item && cJSON_IsNumber(chan_item)) {
                config->vbus_to_chan[i] = chan_item->valueint;
            }
        }
    }

    // 语音通道数
    item = cJSON_GetObjectItem(json, "vcan_number");
    if (item && cJSON_IsNumber(item)) {
        config->vcan_number = item->valueint;
    }

    // 缓冲时间
    item = cJSON_GetObjectItem(json, "buffertime");
    if (item && cJSON_IsNumber(item)) {
        config->buffertime = item->valueint;
    }

    // 掉线时间
    item = cJSON_GetObjectItem(json, "downtime");
    if (item && cJSON_IsNumber(item)) {
        config->downtime = item->valueint;
    }

    // 重置时间
    item = cJSON_GetObjectItem(json, "resettime");
    if (item && cJSON_IsNumber(item)) {
        config->resettime = item->valueint;
    }

    return 0;
}

// 配置管理器接口函数（按命名规范）
int sci_module_load(void *config) {
    return sci_config_load((cfg_sci_t *)config);
}

int sci_module_save(const void *config) {
    return sci_config_save((const cfg_sci_t *)config);
}

int sci_module_validate(const void *config) {
    return sci_config_validate((const cfg_sci_t *)config);
}

int sci_module_to_json(const void *config, cJSON **json) {
    return sci_config_to_json((const cfg_sci_t *)config, json);
}

int sci_module_from_json(const cJSON *json, void *config) {
    return sci_json_to_config(json, (cfg_sci_t *)config);
}

// API处理函数（按命名规范）
/**
 * @brief 获取SCI配置API处理函数
 */
int handle_sci_get(struct MHD_Connection *connection,
                  const char *url,
                  const char *method,
                  const char *upload_data,
                  size_t *upload_data_size,
                  void **con_cls) {
    (void)url; (void)method; (void)upload_data; (void)upload_data_size; (void)con_cls;

    cfg_sci_t config;
    cJSON *response_json = NULL;

    // 加载配置
    if (sci_config_load(&config) != 0) {
        // 发送错误响应
        cJSON *error_json = cJSON_CreateObject();
        cJSON_AddStringToObject(error_json, "error", "Failed to load SCI configuration");
        char *error_str = cJSON_Print(error_json);

        struct MHD_Response *response = MHD_create_response_from_buffer(
            strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
        MHD_add_response_header(response, "Content-Type", "application/json");
        enum MHD_Result result = MHD_queue_response(connection, 500, response);

        free(error_str);
        cJSON_Delete(error_json);
        MHD_destroy_response(response);
        return (result == MHD_YES) ? 0 : -1;
    }

    // 转换为JSON
    if (sci_config_to_json(&config, &response_json) != 0) {
        // 发送错误响应
        cJSON *error_json = cJSON_CreateObject();
        cJSON_AddStringToObject(error_json, "error", "Failed to convert SCI config to JSON");
        char *error_str = cJSON_Print(error_json);

        struct MHD_Response *response = MHD_create_response_from_buffer(
            strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
        MHD_add_response_header(response, "Content-Type", "application/json");
        enum MHD_Result result = MHD_queue_response(connection, 500, response);

        free(error_str);
        cJSON_Delete(error_json);
        MHD_destroy_response(response);
        return (result == MHD_YES) ? 0 : -1;
    }

    // 发送成功响应
    char *json_str = cJSON_Print(response_json);
    struct MHD_Response *response = MHD_create_response_from_buffer(
        strlen(json_str), json_str, MHD_RESPMEM_MUST_COPY);
    MHD_add_response_header(response, "Content-Type", "application/json");
    MHD_add_response_header(response, "Access-Control-Allow-Origin", "*");
    enum MHD_Result result = MHD_queue_response(connection, 200, response);

    free(json_str);
    cJSON_Delete(response_json);
    MHD_destroy_response(response);
    return (result == MHD_YES) ? 0 : -1;
}

/**
 * @brief 设置SCI配置API处理函数
 */
int handle_sci_post(struct MHD_Connection *connection,
                   const char *url,
                   const char *method,
                   const char *upload_data,
                   size_t *upload_data_size,
                   void **con_cls) {
    (void)url; (void)method;

    // 获取POST数据
    cJSON *request_data = api_request_get_json_data(connection, upload_data, upload_data_size, con_cls);
    if (!request_data) {
        if (*upload_data_size == 0) {
            // 发送错误响应
            cJSON *error_json = cJSON_CreateObject();
            cJSON_AddStringToObject(error_json, "error", "No JSON data provided");
            char *error_str = cJSON_Print(error_json);

            struct MHD_Response *response = MHD_create_response_from_buffer(
                strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
            MHD_add_response_header(response, "Content-Type", "application/json");
            enum MHD_Result result = MHD_queue_response(connection, 400, response);

            free(error_str);
            cJSON_Delete(error_json);
            MHD_destroy_response(response);
            return (result == MHD_YES) ? 0 : -1;
        }
        return MHD_YES; // 继续等待数据
    }

    cfg_sci_t config;

    // JSON转配置结构
    if (sci_json_to_config(request_data, &config) != 0) {
        cJSON_Delete(request_data);
        // 发送错误响应
        cJSON *error_json = cJSON_CreateObject();
        cJSON_AddStringToObject(error_json, "error", "Invalid SCI configuration data");
        char *error_str = cJSON_Print(error_json);

        struct MHD_Response *response = MHD_create_response_from_buffer(
            strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
        MHD_add_response_header(response, "Content-Type", "application/json");
        enum MHD_Result result = MHD_queue_response(connection, 400, response);

        free(error_str);
        cJSON_Delete(error_json);
        MHD_destroy_response(response);
        return (result == MHD_YES) ? 0 : -1;
    }

    // 保存配置
    if (sci_config_save(&config) != 0) {
        cJSON_Delete(request_data);
        // 发送错误响应
        cJSON *error_json = cJSON_CreateObject();
        cJSON_AddStringToObject(error_json, "error", "Failed to save SCI configuration");
        char *error_str = cJSON_Print(error_json);

        struct MHD_Response *response = MHD_create_response_from_buffer(
            strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
        MHD_add_response_header(response, "Content-Type", "application/json");
        enum MHD_Result result = MHD_queue_response(connection, 500, response);

        free(error_str);
        cJSON_Delete(error_json);
        MHD_destroy_response(response);
        return (result == MHD_YES) ? 0 : -1;
    }

    // 返回成功响应
    cJSON *response_data = cJSON_CreateObject();
    cJSON_AddStringToObject(response_data, "message", "SCI configuration saved successfully");
    char *json_str = cJSON_Print(response_data);

    struct MHD_Response *response = MHD_create_response_from_buffer(
        strlen(json_str), json_str, MHD_RESPMEM_MUST_COPY);
    MHD_add_response_header(response, "Content-Type", "application/json");
    MHD_add_response_header(response, "Access-Control-Allow-Origin", "*");
    enum MHD_Result result = MHD_queue_response(connection, 200, response);

    free(json_str);
    cJSON_Delete(response_data);
    cJSON_Delete(request_data);
    MHD_destroy_response(response);
    return (result == MHD_YES) ? 0 : -1;
}

/**
 * @brief 初始化SCI配置模块
 */
int sci_config_init(void) {
    // 初始化配置模块结构
    s_sci_module.module_name = "sci";
    s_sci_module.config_file_path = SCI_CONFIG_FILE;
    s_sci_module.config_struct_size = sizeof(cfg_sci_t);
    s_sci_module.load_config = sci_module_load;
    s_sci_module.save_config = sci_module_save;
    s_sci_module.validate_config = sci_module_validate;
    s_sci_module.config_to_json = sci_module_to_json;
    s_sci_module.json_to_config = sci_module_from_json;

    // 注册到配置管理器
    if (config_manager_register(&s_sci_module) != 0) {
        printf("Failed to register SCI config module\n");
        return -1;
    }

    printf("SCI config module initialized\n");
    return 0;
}

/**
 * @brief 清理SCI配置模块
 */
void sci_config_cleanup(void) {
    printf("SCI config module cleanup\n");
}
