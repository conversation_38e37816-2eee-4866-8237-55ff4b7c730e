/**
 * @file center_config.c
 * @brief 呼叫中心配置管理模块实现
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#include "center_config.h"
#include "config_interface.h"
#include "global_config.h"
#include "file_utils.h"
#include "network_utils.h"
#include "api_router.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <arpa/inet.h>

// 静态变量（按命名规范）
static config_module_t s_center_module;

/**
 * @brief 设置默认配置 - 严格对应旧项目默认值
 */
void center_config_set_default(cfg_center_t *config) {
    if (!config) return;
    
    memset(config, 0, sizeof(cfg_center_t));
    
    // 对应旧项目set_default_value_center()的默认值
    config->center_no = MAKEADDR(CENTER_DEFAULT_DS, CENTER_DEFAULT_SW, CENTER_DEFAULT_BS, 28);
    config->center_outssi = CENTER_DEFAULT_OUTSSI;
    config->center_inssi = CENTER_DEFAULT_INSSI;
    config->vchan_sum = CENTER_DEFAULT_VCHAN_SUM;
    config->center_voice_port = CENTER_DEFAULT_VOICE_PORT;
    config->listen_agent_port = CENTER_DEFAULT_AGENT_PORT;
    config->peer_net_type = 0;                              // ePeerNetUnicast
    config->send_all_agent_ip = inet_addr(CENTER_DEFAULT_AGENT_IP);
    config->send_to_agent_port = CENTER_DEFAULT_AGENT_SEND_PORT;
    config->inssi_num = CENTER_DEFAULT_INSSI_NUM;
    config->spec_function = 0;                              // 默认关闭所有特殊功能
}

/**
 * @brief 加载呼叫中心配置
 */
int center_config_load(cfg_center_t *config) {
    if (!config) return -1;
    
    // 获取动态配置路径
    char config_path[512];
    if (global_config_get_path(CENTER_CONFIG_FILE, config_path, sizeof(config_path)) != 0) {
        printf("Warning: Failed to get center config path, using defaults\n");
        center_config_set_default(config);
        return 0;
    }
    
    // 使用公共工具模块读取二进制配置文件
    if (file_utils_read_binary(config_path, 0, sizeof(cfg_center_t), config) != 0) {
        printf("Warning: Failed to read center config from %s, using defaults\n", config_path);
        center_config_set_default(config);
        return 0; // 使用默认配置不算错误
    }
    
    return 0;
}

/**
 * @brief 保存呼叫中心配置
 */
int center_config_save(const cfg_center_t *config) {
    if (!config) return -1;
    
    // 验证配置
    if (center_config_validate(config) != 0) {
        return -1;
    }
    
    // 获取动态配置路径
    char config_path[512];
    if (global_config_get_path(CENTER_CONFIG_FILE, config_path, sizeof(config_path)) != 0) {
        return -1;
    }
    
    // 使用公共工具模块写入二进制配置文件
    return file_utils_write_binary(config_path, 0, sizeof(cfg_center_t), config);
}

/**
 * @brief 验证呼叫中心配置 - 增强验证逻辑
 */
int center_config_validate(const cfg_center_t *config) {
    if (!config) return -1;
    
    // 验证地址编码范围 - 对应旧项目的DS/SW/BS范围
    uint32_t ds = GETDS(config->center_no);
    uint32_t sw = GETSW(config->center_no);
    uint32_t bs = GETBS(config->center_no);
    
    if (ds < 1 || ds > 63) {
        printf("Invalid center DS: %d (valid range: 1-63)\n", ds);
        return -1;
    }
    
    if (sw < 1 || sw > 255) {
        printf("Invalid center SW: %d (valid range: 1-255)\n", sw);
        return -1;
    }
    
    if (bs < 1 || bs > 31) {
        printf("Invalid center BS: %d (valid range: 1-31)\n", bs);
        return -1;
    }
    
    // 验证语音通道数 - 对应旧项目范围
    if (config->vchan_sum < 1 || config->vchan_sum > 64) {
        printf("Invalid voice channel count: %d (valid range: 1-64)\n", config->vchan_sum);
        return -1;
    }
    
    // 验证端口范围
    if (config->center_voice_port == 0 || config->listen_agent_port == 0) {
        printf("Invalid port configuration: voice_port=%d, agent_port=%d\n", 
               config->center_voice_port, config->listen_agent_port);
        return -1;
    }
    
    // 验证网络类型 - 0:单播, 1:广播, 2:组播
    if (config->peer_net_type > 2) {
        printf("Invalid peer network type: %d (valid range: 0-2)\n", config->peer_net_type);
        return -1;
    }
    
    // 验证应答号个数范围 - 对应旧项目范围
    if (config->inssi_num > 200) {
        printf("Invalid INSSI number: %d (max: 200)\n", config->inssi_num);
        return -1;
    }
    
    return 0;
}

/**
 * @brief 配置结构转JSON - 支持完整的11字段输出
 */
int center_config_to_json(const cfg_center_t *config, cJSON **json) {
    if (!config || !json) return -1;
    
    *json = cJSON_CreateObject();
    if (!*json) return -1;
    
    // 输出完整的11个核心参数
    cJSON_AddNumberToObject(*json, "center_no", config->center_no);
    
    // 分解地址编码便于前端显示和编辑
    cJSON_AddNumberToObject(*json, "center_ds", GETDS(config->center_no));
    cJSON_AddNumberToObject(*json, "center_sw", GETSW(config->center_no));
    cJSON_AddNumberToObject(*json, "center_bs", GETBS(config->center_no));
    
    // SSI号码以十六进制字符串形式输出，便于前端显示
    char ssi_str[16];
    snprintf(ssi_str, sizeof(ssi_str), "%X", config->center_outssi);
    cJSON_AddStringToObject(*json, "center_outssi", ssi_str);
    
    snprintf(ssi_str, sizeof(ssi_str), "%X", config->center_inssi);
    cJSON_AddStringToObject(*json, "center_inssi", ssi_str);
    
    cJSON_AddNumberToObject(*json, "vchan_sum", config->vchan_sum);
    cJSON_AddNumberToObject(*json, "center_voice_port", config->center_voice_port);
    cJSON_AddNumberToObject(*json, "listen_agent_port", config->listen_agent_port);
    cJSON_AddNumberToObject(*json, "peer_net_type", config->peer_net_type);
    
    // IP地址转换
    char ip_str[16];
    if (ip_utils_binary_to_string(config->send_all_agent_ip, ip_str, sizeof(ip_str)) == 0) {
        cJSON_AddStringToObject(*json, "send_all_agent_ip", ip_str);
    } else {
        cJSON_AddStringToObject(*json, "send_all_agent_ip", "0.0.0.0");
    }
    
    cJSON_AddNumberToObject(*json, "send_to_agent_port", config->send_to_agent_port);
    cJSON_AddNumberToObject(*json, "inssi_num", config->inssi_num);
    cJSON_AddNumberToObject(*json, "spec_function", config->spec_function);
    
    return 0;
}

/**
 * @brief JSON转配置结构 - 支持完整的11字段解析
 */
int center_json_to_config(const cJSON *json, cfg_center_t *config) {
    if (!json || !config) return -1;
    
    // 先设置默认值
    center_config_set_default(config);
    
    // 解析JSON字段
    cJSON *item;
    
    // 处理地址编码 - 优先使用分解的DS/SW/BS，如果没有则使用center_no
    int ds = CENTER_DEFAULT_DS, sw = CENTER_DEFAULT_SW, bs = CENTER_DEFAULT_BS;
    
    item = cJSON_GetObjectItem(json, "center_ds");
    if (item && cJSON_IsNumber(item)) {
        ds = item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "center_sw");
    if (item && cJSON_IsNumber(item)) {
        sw = item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "center_bs");
    if (item && cJSON_IsNumber(item)) {
        bs = item->valueint;
    }
    
    config->center_no = MAKEADDR(ds, sw, bs, 28);
    
    // 直接设置center_no（如果提供的话）
    item = cJSON_GetObjectItem(json, "center_no");
    if (item && cJSON_IsNumber(item)) {
        config->center_no = (uint32_t)item->valueint;
    }
    
    // 处理SSI号码 - 支持十六进制字符串输入
    item = cJSON_GetObjectItem(json, "center_outssi");
    if (item && cJSON_IsString(item)) {
        config->center_outssi = (uint32_t)strtoul(item->valuestring, NULL, 16);
    }
    
    item = cJSON_GetObjectItem(json, "center_inssi");
    if (item && cJSON_IsString(item)) {
        config->center_inssi = (uint32_t)strtoul(item->valuestring, NULL, 16);
    }
    
    // 处理其他数字字段
    item = cJSON_GetObjectItem(json, "vchan_sum");
    if (item && cJSON_IsNumber(item)) {
        config->vchan_sum = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "center_voice_port");
    if (item && cJSON_IsNumber(item)) {
        config->center_voice_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "listen_agent_port");
    if (item && cJSON_IsNumber(item)) {
        config->listen_agent_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "peer_net_type");
    if (item && cJSON_IsNumber(item)) {
        config->peer_net_type = (uint8_t)item->valueint;
    }
    
    // 处理代理IP地址
    item = cJSON_GetObjectItem(json, "send_all_agent_ip");
    if (item && cJSON_IsString(item)) {
        uint32_t temp_ip;
        if (ip_utils_string_to_binary(item->valuestring, &temp_ip) == 0) {
            config->send_all_agent_ip = temp_ip;
        } else {
            config->send_all_agent_ip = inet_addr(CENTER_DEFAULT_AGENT_IP);
        }
    }
    
    item = cJSON_GetObjectItem(json, "send_to_agent_port");
    if (item && cJSON_IsNumber(item)) {
        config->send_to_agent_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "inssi_num");
    if (item && cJSON_IsNumber(item)) {
        config->inssi_num = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "spec_function");
    if (item && cJSON_IsNumber(item)) {
        config->spec_function = (uint16_t)item->valueint;
    }
    
    return 0;
}

/**
 * @brief 获取呼叫中心配置信息（API处理函数）
 */
int handle_center_get(struct MHD_Connection *connection, 
                     const char *url, 
                     const char *method,
                     const char *upload_data, 
                     size_t *upload_data_size,
                     void **con_cls) {
    (void)url; (void)method; (void)upload_data; (void)upload_data_size; (void)con_cls;
    
    cfg_center_t config;
    cJSON *json = NULL;
    
    // 加载配置
    if (center_config_load(&config) != 0) {
        api_response_t *response = api_response_create_error(500, "Failed to load center config");
        api_response_send(connection, response);
        api_response_free(response);
        return -1;
    }

    // 转换为JSON
    if (center_config_to_json(&config, &json) != 0) {
        api_response_t *response = api_response_create_error(500, "Failed to convert config to JSON");
        api_response_send(connection, response);
        api_response_free(response);
        return -1;
    }

    // 发送响应
    api_response_t *response = api_response_create_success(json);
    api_response_send(connection, response);
    api_response_free(response);
    cJSON_Delete(json);

    return 0;
}

/**
 * @brief 设置呼叫中心配置信息（API处理函数）
 */
int handle_center_post(struct MHD_Connection *connection, 
                      const char *url, 
                      const char *method,
                      const char *upload_data, 
                      size_t *upload_data_size,
                      void **con_cls) {
    (void)url; (void)method;
    
    printf("DEBUG: handle_center_post called\n");
    
    // 获取POST数据
    cJSON *json = api_request_get_json_data(connection, upload_data, upload_data_size, con_cls);
    if (!json) {
        printf("DEBUG: Failed to get JSON data\n");
        api_response_t *response = api_response_create_error(400, "Invalid JSON data");
        api_response_send(connection, response);
        api_response_free(response);
        return -1;
    }
    
    printf("DEBUG: JSON data received successfully\n");

    cfg_center_t config;

    // JSON转配置结构
    if (center_json_to_config(json, &config) != 0) {
        cJSON_Delete(json);
        api_response_t *response = api_response_create_error(400, "Invalid config data");
        api_response_send(connection, response);
        api_response_free(response);
        return -1;
    }

    // 保存配置
    if (center_config_save(&config) != 0) {
        cJSON_Delete(json);
        api_response_t *response = api_response_create_error(500, "Failed to save config");
        api_response_send(connection, response);
        api_response_free(response);
        return -1;
    }

    cJSON_Delete(json);

    // 返回成功响应
    cJSON *success_json = cJSON_CreateObject();
    cJSON_AddStringToObject(success_json, "message", "Center config saved successfully");

    api_response_t *response = api_response_create_success(success_json);
    api_response_send(connection, response);
    api_response_free(response);
    cJSON_Delete(success_json);

    return 0;
}

// 配置模块接口实现（按命名规范）
static int center_module_load(void *config) {
    return center_config_load((cfg_center_t *)config);
}

static int center_module_save(const void *config) {
    return center_config_save((const cfg_center_t *)config);
}

static int center_module_validate(const void *config) {
    return center_config_validate((const cfg_center_t *)config);
}

static int center_module_to_json(const void *config, cJSON **json) {
    return center_config_to_json((const cfg_center_t *)config, json);
}

static int center_module_from_json(const cJSON *json, void *config) {
    return center_json_to_config(json, (cfg_center_t *)config);
}

/**
 * @brief 初始化呼叫中心配置模块
 */
int center_config_init(void) {
    // 初始化配置模块结构
    s_center_module.module_name = "center";
    s_center_module.config_file_path = CENTER_CONFIG_FILE;
    s_center_module.config_struct_size = sizeof(cfg_center_t);
    s_center_module.load_config = center_module_load;
    s_center_module.save_config = center_module_save;
    s_center_module.validate_config = center_module_validate;
    s_center_module.config_to_json = center_module_to_json;
    s_center_module.json_to_config = center_module_from_json;

    // 注册到配置管理器
    if (config_manager_register(&s_center_module) != 0) {
        printf("Failed to register center config module\n");
        return -1;
    }

    printf("Center config module initialized\n");
    return 0;
}

/**
 * @brief 清理呼叫中心配置模块
 */
void center_config_cleanup(void) {
    // 清理模块资源（如果有的话）
    printf("Center config module cleaned up\n");
}
