/**
 * @file board_ntp.c
 * @brief NTP时间同步配置管理实现 - 对应旧项目0ntp.c
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#include "board_ntp.h"
#include "config_interface.h"
#include "global_config.h"
#include "file_utils.h"
#include "api_router.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// 静态变量（按命名规范）
static config_module_t s_ntp_module;

/**
 * @brief 设置NTP配置默认值
 */
void board_ntp_set_default(cfg_ntp_t *config) {
    if (!config) return;
    
    memset(config, 0, sizeof(cfg_ntp_t));
    strncpy(config->ntp_server, NTP_DEFAULT_SERVER, sizeof(config->ntp_server) - 1);
    config->enable_client = 1;  // 默认启用客户端模式
    config->enable_server = 0;  // 默认不启用服务器模式
}

/**
 * @brief 加载NTP配置 - 读取INI格式配置文件
 */
int board_ntp_load(cfg_ntp_t *config) {
    if (!config) return -1;
    
    // 先设置默认值
    board_ntp_set_default(config);
    
    // 构建配置文件路径（使用global_config获取路径）
    char config_path[512];
    if (global_config_get_path(board_ntp_FILE, config_path, sizeof(config_path)) != 0) {
        // 如果全局配置失败，使用固定路径作为兜底
        strcpy(config_path, "/etc/ntp-setting");
    }
    
    // 检查文件是否存在
    if (!file_utils_exists(config_path)) {
        printf("Warning: NTP config file %s not found, using defaults\n", config_path);
        return 0; // 文件不存在使用默认配置
    }
    
    // 读取INI格式配置文件
    FILE *fp = fopen(config_path, "r");
    if (!fp) {
        printf("Warning: Failed to open NTP config file %s\n", config_path);
        return 0;
    }
    
    char line[128];
    char key[32], value[64];
    
    while (fgets(line, sizeof(line), fp)) {
        // 跳过注释和空行
        if (line[0] == '#' || line[0] == '\n' || line[0] == '\r') {
            continue;
        }
        
        // 解析key=value格式
        if (sscanf(line, "%31[^=]=%63s", key, value) == 2) {
            if (strcmp(key, "UseNtp") == 0) {
                config->enable_client = (uint8_t)atoi(value);
            } else if (strcmp(key, "NtpAddr") == 0) {
                strncpy(config->ntp_server, value, sizeof(config->ntp_server) - 1);
                config->ntp_server[sizeof(config->ntp_server) - 1] = '\0';
            } else if (strcmp(key, "NtpServ") == 0) {
                config->enable_server = (uint8_t)atoi(value);
            }
        }
    }
    
    fclose(fp);
    return 0;
}

/**
 * @brief 保存NTP配置 - 写入INI格式配置文件
 */
int board_ntp_save(const cfg_ntp_t *config) {
    if (!config) return -1;
    
    // 验证配置
    if (board_ntp_validate(config) != 0) {
        return -1;
    }
    
    // 构建配置文件路径（使用global_config获取路径）
    char config_path[512];
    if (global_config_get_path(board_ntp_FILE, config_path, sizeof(config_path)) != 0) {
        // 如果全局配置失败，使用固定路径作为兜底
        strcpy(config_path, "/etc/ntp-setting");
    }
    
    // 打开配置文件进行写入
    FILE *fp = fopen(config_path, "w");
    if (!fp) {
        printf("Error: Failed to open NTP config file %s for writing\n", config_path);
        return -1;
    }
    
    // 写入INI格式配置（严格按照旧项目格式）
    fprintf(fp, "UseNtp=%d\n", config->enable_client);
    fprintf(fp, "NtpAddr=%s\n", config->ntp_server);
    fprintf(fp, "NtpServ=%d\n", config->enable_server);
    
    fclose(fp);
    return 0;
}

/**
 * @brief 验证NTP配置 - 对应旧项目验证规则
 */
int board_ntp_validate(const cfg_ntp_t *config) {
    if (!config) return -1;
    
    // 验证NTP服务器地址（当启用客户端模式时必须有服务器地址）
    if (config->enable_client && strlen(config->ntp_server) == 0) {
        printf("Invalid NTP config: client mode enabled but no server address\n");
        return -1;
    }
    
    // 验证服务器地址长度（对应旧项目50字符限制）
    if (strlen(config->ntp_server) >= 50) {
        printf("Invalid NTP server: address too long (max 49 characters)\n");
        return -1;
    }
    
    return 0;
}

/**
 * @brief NTP配置转JSON - 对应旧项目参数
 */
int board_ntp_to_json(const cfg_ntp_t *config, cJSON **json) {
    if (!config || !json) return -1;
    
    *json = cJSON_CreateObject();
    if (!*json) return -1;
    
    cJSON_AddStringToObject(*json, "ntp_server", config->ntp_server);
    cJSON_AddBoolToObject(*json, "enable_client", config->enable_client);
    cJSON_AddBoolToObject(*json, "enable_server", config->enable_server);
    
    return 0;
}

/**
 * @brief JSON转NTP配置
 */
int ntp_json_to_config(const cJSON *json, cfg_ntp_t *config) {
    if (!json || !config) return -1;
    
    // 先设置默认值
    board_ntp_set_default(config);
    
    // 解析JSON字段
    cJSON *item;
    
    item = cJSON_GetObjectItem(json, "ntp_server");
    if (item && cJSON_IsString(item)) {
        strncpy(config->ntp_server, item->valuestring, sizeof(config->ntp_server) - 1);
        config->ntp_server[sizeof(config->ntp_server) - 1] = '\0';
    }
    
    item = cJSON_GetObjectItem(json, "enable_client");
    if (item && cJSON_IsBool(item)) {
        config->enable_client = cJSON_IsTrue(item) ? 1 : 0;
    }
    
    item = cJSON_GetObjectItem(json, "enable_server");
    if (item && cJSON_IsBool(item)) {
        config->enable_server = cJSON_IsTrue(item) ? 1 : 0;
    }

    return 0;
}

// 配置管理器接口函数（按命名规范）
int ntp_module_load(void *config) {
    return board_ntp_load((cfg_ntp_t *)config);
}

int ntp_module_save(const void *config) {
    return board_ntp_save((const cfg_ntp_t *)config);
}

int ntp_module_validate(const void *config) {
    return board_ntp_validate((const cfg_ntp_t *)config);
}

int ntp_module_to_json(const void *config, cJSON **json) {
    return board_ntp_to_json((const cfg_ntp_t *)config, json);
}

int ntp_module_from_json(const cJSON *json, void *config) {
    return ntp_json_to_config(json, (cfg_ntp_t *)config);
}

// API处理函数（按命名规范）
/**
 * @brief 获取NTP配置API处理函数
 */
int handle_board_ntp_get(struct MHD_Connection *connection,
                  const char *url,
                  const char *method,
                  const char *upload_data,
                  size_t *upload_data_size,
                  void **con_cls) {
    cfg_ntp_t config;
    cJSON *json_response = NULL;
    char *json_string = NULL;
    struct MHD_Response *response = NULL;

    // 加载配置
    if (board_ntp_load(&config) != 0) {
        const char *error_msg = "{\"error\":\"Failed to load NTP config\"}";
        response = MHD_create_response_from_buffer(strlen(error_msg),
                                                  (void*)error_msg,
                                                  MHD_RESPMEM_PERSISTENT);
        if (response) {
            MHD_add_response_header(response, "Content-Type", "application/json");
            MHD_queue_response(connection, 500, response);
            MHD_destroy_response(response);
            return -1;
        }
        return -1;
    }

    // 转换为JSON
    if (board_ntp_to_json(&config, &json_response) != 0) {
        const char *error_msg = "{\"error\":\"Failed to convert config to JSON\"}";
        response = MHD_create_response_from_buffer(strlen(error_msg),
                                                  (void*)error_msg,
                                                  MHD_RESPMEM_PERSISTENT);
        if (response) {
            MHD_add_response_header(response, "Content-Type", "application/json");
            MHD_queue_response(connection, 500, response);
            MHD_destroy_response(response);
            return -1;
        }
        return -1;
    }

    // 转换JSON为字符串
    json_string = cJSON_Print(json_response);
    cJSON_Delete(json_response);

    if (!json_string) {
        const char *error_msg = "{\"error\":\"Failed to serialize JSON\"}";
        response = MHD_create_response_from_buffer(strlen(error_msg),
                                                  (void*)error_msg,
                                                  MHD_RESPMEM_PERSISTENT);
        if (response) {
            MHD_add_response_header(response, "Content-Type", "application/json");
            MHD_queue_response(connection, 500, response);
            MHD_destroy_response(response);
            return -1;
        }
        return -1;
    }

    // 创建响应
    response = MHD_create_response_from_buffer(strlen(json_string),
                                              json_string,
                                              MHD_RESPMEM_MUST_FREE);
    if (response) {
        MHD_add_response_header(response, "Content-Type", "application/json");
        MHD_queue_response(connection, 200, response);
        MHD_destroy_response(response);
        return 0; // 成功
    } else {
        free(json_string);
        return -1; // 失败
    }
}

/**
 * @brief 设置NTP配置API处理函数
 */
int handle_board_ntp_post(struct MHD_Connection *connection,
                   const char *url,
                   const char *method,
                   const char *upload_data,
                   size_t *upload_data_size,
                   void **con_cls) {
    cfg_ntp_t config;
    cJSON *request_json = NULL;
    struct MHD_Response *response = NULL;

    // 获取POST数据
    request_json = api_request_get_json_data(connection, upload_data, upload_data_size, con_cls);
    if (!request_json) {
        // 如果数据还在接收中，返回继续处理
        return 0; // 继续处理
    }

    // JSON转配置结构
    if (ntp_json_to_config(request_json, &config) != 0) {
        cJSON_Delete(request_json);
        const char *error_msg = "{\"error\":\"Invalid config data\"}";
        response = MHD_create_response_from_buffer(strlen(error_msg),
                                                  (void*)error_msg,
                                                  MHD_RESPMEM_PERSISTENT);
        if (response) {
            MHD_add_response_header(response, "Content-Type", "application/json");
            MHD_queue_response(connection, 400, response);
            MHD_destroy_response(response);
        }
        return -1;
    }

    cJSON_Delete(request_json);

    // 保存配置
    if (board_ntp_save(&config) != 0) {
        const char *error_msg = "{\"error\":\"Failed to save NTP config\"}";
        response = MHD_create_response_from_buffer(strlen(error_msg),
                                                  (void*)error_msg,
                                                  MHD_RESPMEM_PERSISTENT);
        if (response) {
            MHD_add_response_header(response, "Content-Type", "application/json");
            MHD_queue_response(connection, 500, response);
            MHD_destroy_response(response);
        }
        return -1;
    }

    // 成功响应
    const char *success_msg = "{\"status\":\"success\"}";
    response = MHD_create_response_from_buffer(strlen(success_msg),
                                              (void*)success_msg,
                                              MHD_RESPMEM_PERSISTENT);
    if (response) {
        MHD_add_response_header(response, "Content-Type", "application/json");
        MHD_queue_response(connection, 200, response);
        MHD_destroy_response(response);
        return 0; // 成功
    }

    return -1; // 失败
}

/**
 * @brief 初始化NTP配置模块
 */
int board_ntp_init(void) {
    // 初始化配置模块结构
    s_ntp_module.module_name = "ntp";
    s_ntp_module.config_file_path = board_ntp_FILE;
    s_ntp_module.config_struct_size = sizeof(cfg_ntp_t);
    s_ntp_module.load_config = ntp_module_load;
    s_ntp_module.save_config = ntp_module_save;
    s_ntp_module.validate_config = ntp_module_validate;
    s_ntp_module.config_to_json = ntp_module_to_json;
    s_ntp_module.json_to_config = ntp_module_from_json;

    // 注册到配置管理器
    if (config_manager_register(&s_ntp_module) != 0) {
        printf("Failed to register NTP config module\n");
        return -1;
    }

    printf("NTP config module initialized\n");
    return 0;
}

/**
 * @brief 清理NTP配置模块
 */
void board_ntp_cleanup(void) {
    printf("NTP config module cleanup\n");
}
