/**
 * @file recorder_config.c
 * @brief 录音模块配置管理实现 - 对应旧项目0recorder.c（简化版）
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#include "recorder_config.h"
#include "config_interface.h"
#include "global_config.h"
#include "file_utils.h"
#include "network_utils.h"
#include "api_router.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <arpa/inet.h>
#include <stdbool.h>

// 静态变量（按命名规范）
static config_module_t s_recorder_module;

/**
 * @brief 设置录音配置默认值 - 严格对应旧项目stCfgPeerBase默认值
 */
void recorder_config_set_default(cfg_recorder_t *config) {
    if (!config) return;
    
    memset(config, 0, sizeof(cfg_recorder_t));
    
    // 设置基站数量（新项目固定为1）
    config->peer_sum = 1;
    
    // 对应旧项目第一个基站的默认值
    config->peer[0].peer_ip = inet_addr(RECORDER_DEFAULT_SERVER_IP);
    config->peer[0].peer_port = RECORDER_DEFAULT_SERVER_PORT;
    config->peer[0].device_type = RECORDER_DEFAULT_DEVICE_TYPE;  // 录音模块
    config->peer[0].voice_port = RECORDER_DEFAULT_SERVER_PORT + 1;
    config->peer[0].data_port = RECORDER_DEFAULT_SERVER_PORT + 2;
    config->peer[0].timeout = RECORDER_DEFAULT_TIMEOUT;
    
    // 其余基站初始化为0
    for (int i = 1; i < 8; i++) {
        memset(&config->peer[i], 0, sizeof(config->peer[i]));
    }
}

/**
 * @brief 加载录音配置 - 严格对应旧项目stCfgPeerBase格式
 */
int recorder_config_load(cfg_recorder_t *config) {
    char config_path[512];
    
    if (!config) return -1;
    
    // 获取动态配置路径
    if (global_config_get_path(RECORDER_CONFIG_FILE, config_path, sizeof(config_path)) != 0) {
        printf("Warning: Failed to get recorder config path, using defaults\n");
        recorder_config_set_default(config);
        return 0;
    }
    
    // 读取配置文件（使用相同的结构体格式）
    if (file_utils_read_binary(config_path, 0, sizeof(cfg_recorder_t), config) == 0) {
        // 成功读取配置，验证数据有效性
        if (config->peer_sum > 8) {
            printf("Warning: Invalid peer_sum %d, using defaults\n", config->peer_sum);
            recorder_config_set_default(config);
            return 0;
        }
        if (config->peer_sum == 0) {
            printf("Warning: No peers configured, setting to 1\n");
            config->peer_sum = 1;
        }
        // 配置有效，直接使用
        return 0;
    } else {
        // 读取失败，使用默认配置
        printf("Warning: Failed to read recorder config from %s, using defaults\n", config_path);
        recorder_config_set_default(config);
        return 0;
    }
}

/**
 * @brief 保存录音配置 - 严格对应旧项目stCfgPeerBase格式
 */
int recorder_config_save(const cfg_recorder_t *config) {
    char config_path[512];
    
    if (!config) return -1;
    
    // 验证配置
    if (recorder_config_validate(config) != 0) {
        return -1;
    }
    
    // 获取动态配置路径
    if (global_config_get_path(RECORDER_CONFIG_FILE, config_path, sizeof(config_path)) != 0) {
        printf("Error: Failed to get recorder config path for saving\n");
        return -1;
    }
    
    // 直接写入配置（使用相同的结构体格式）
    return file_utils_write_binary(config_path, 0, sizeof(cfg_recorder_t), config);
}

/**
 * @brief 验证录音配置 - 对应旧项目stCfgPeerBase验证逻辑
 */
int recorder_config_validate(const cfg_recorder_t *config) {
    if (!config) return -1;
    
    // 验证基站数量
    if (config->peer_sum > 8) {
        printf("Invalid peer_sum: %d (must be 1-8)\n", config->peer_sum);
        return -1;
    }
    
    if (config->peer_sum == 0) {
        printf("Invalid peer_sum: 0 (must be at least 1)\n");
        return -1;
    }
    
    // 验证每个基站配置
    for (int i = 0; i < config->peer_sum; i++) {
        // 验证基站IP地址（不为0）
        if (config->peer[i].peer_ip == 0) {
            printf("Invalid peer[%d] IP: 0\n", i);
            return -1;
        }
        
        // 验证端口范围
        if (config->peer[i].peer_port == 0) {
            printf("Invalid peer[%d] port: 0\n", i);
            return -1;
        }
        
        // 验证设备类型（录音模块0x13或最小基站0x17）
        if (config->peer[i].device_type != 0x13 && config->peer[i].device_type != 0x17) {
            printf("Invalid peer[%d] device_type: 0x%02x\n", i, config->peer[i].device_type);
            return -1;
        }
        
        // 验证超时时间
        if (config->peer[i].timeout < 1 || config->peer[i].timeout > 300) {
            printf("Invalid peer[%d] timeout: %d (must be 1-300)\n", i, config->peer[i].timeout);
            return -1;
        }
    }
    
    return 0;
}

/**
 * @brief 录音配置转JSON - 对应旧项目stCfgPeerBase格式
 */
int recorder_config_to_json(const cfg_recorder_t *config, cJSON **json) {
    if (!config || !json) return -1;
    
    *json = cJSON_CreateObject();
    if (!*json) return -1;
    
    // 基站数量
    cJSON_AddNumberToObject(*json, "peer_sum", config->peer_sum);
    
    // 基站配置数组
    cJSON *peers_array = cJSON_CreateArray();
    for (int i = 0; i < config->peer_sum && i < 8; i++) {
        cJSON *peer_obj = cJSON_CreateObject();
        
        // 使用公共工具模块转换IP地址为字符串
        char ip_str[16];
        if (ip_utils_binary_to_string(config->peer[i].peer_ip, ip_str, sizeof(ip_str)) == 0) {
            cJSON_AddStringToObject(peer_obj, "peer_ip", ip_str);
        } else {
            cJSON_AddStringToObject(peer_obj, "peer_ip", RECORDER_DEFAULT_SERVER_IP);
        }
        
        cJSON_AddNumberToObject(peer_obj, "peer_port", config->peer[i].peer_port);
        cJSON_AddNumberToObject(peer_obj, "device_type", config->peer[i].device_type);
        cJSON_AddNumberToObject(peer_obj, "voice_port", config->peer[i].voice_port);
        cJSON_AddNumberToObject(peer_obj, "data_port", config->peer[i].data_port);
        cJSON_AddNumberToObject(peer_obj, "timeout", config->peer[i].timeout);
        
        cJSON_AddItemToArray(peers_array, peer_obj);
    }
    cJSON_AddItemToObject(*json, "peers", peers_array);
    
    return 0;
}

/**
 * @brief JSON转录音配置 - 对应旧项目stCfgPeerBase格式
 */
int recorder_json_to_config(const cJSON *json, cfg_recorder_t *config) {
    if (!json || !config) return -1;
    
    // 先设置默认值
    recorder_config_set_default(config);
    
    // 解析基站数量
    cJSON *item = cJSON_GetObjectItem(json, "peer_sum");
    if (item && cJSON_IsNumber(item)) {
        int peer_sum = item->valueint;
        if (peer_sum < 1 || peer_sum > 8) {
            printf("Invalid peer_sum: %d\n", peer_sum);
            return -1;
        }
        config->peer_sum = (uint8_t)peer_sum;
    }
    
    // 解析基站配置数组
    cJSON *peers_array = cJSON_GetObjectItem(json, "peers");
    if (peers_array && cJSON_IsArray(peers_array)) {
        int array_size = cJSON_GetArraySize(peers_array);
        int max_peers = (array_size < config->peer_sum) ? array_size : config->peer_sum;
        
        for (int i = 0; i < max_peers && i < 8; i++) {
            cJSON *peer_obj = cJSON_GetArrayItem(peers_array, i);
            if (!peer_obj || !cJSON_IsObject(peer_obj)) continue;
            
            // 解析基站IP地址
            cJSON *peer_ip = cJSON_GetObjectItem(peer_obj, "peer_ip");
            if (peer_ip && cJSON_IsString(peer_ip)) {
                uint32_t temp_ip;
                if (ip_utils_string_to_binary(peer_ip->valuestring, &temp_ip) == 0) {
                    config->peer[i].peer_ip = temp_ip;
                }
            }
            
            // 解析其他字段
            cJSON *peer_port = cJSON_GetObjectItem(peer_obj, "peer_port");
            if (peer_port && cJSON_IsNumber(peer_port)) {
                config->peer[i].peer_port = (uint16_t)peer_port->valueint;
            }
            
            cJSON *device_type = cJSON_GetObjectItem(peer_obj, "device_type");
            if (device_type && cJSON_IsNumber(device_type)) {
                config->peer[i].device_type = (uint8_t)device_type->valueint;
            }
            
            cJSON *voice_port = cJSON_GetObjectItem(peer_obj, "voice_port");
            if (voice_port && cJSON_IsNumber(voice_port)) {
                config->peer[i].voice_port = (uint16_t)voice_port->valueint;
            }
            
            cJSON *data_port = cJSON_GetObjectItem(peer_obj, "data_port");
            if (data_port && cJSON_IsNumber(data_port)) {
                config->peer[i].data_port = (uint16_t)data_port->valueint;
            }
            
            cJSON *timeout = cJSON_GetObjectItem(peer_obj, "timeout");
            if (timeout && cJSON_IsNumber(timeout)) {
                config->peer[i].timeout = (uint16_t)timeout->valueint;
            }
        }
    }
    
    return 0;
}

// 配置管理器接口实现（按命名规范）
int recorder_module_load(void *config) {
    return recorder_config_load((cfg_recorder_t *)config);
}

int recorder_module_save(const void *config) {
    return recorder_config_save((const cfg_recorder_t *)config);
}

int recorder_module_validate(const void *config) {
    return recorder_config_validate((const cfg_recorder_t *)config);
}

int recorder_module_to_json(const void *config, cJSON **json) {
    return recorder_config_to_json((const cfg_recorder_t *)config, json);
}

int recorder_module_from_json(const cJSON *json, void *config) {
    return recorder_json_to_config(json, (cfg_recorder_t *)config);
}

// API处理函数（按命名规范）
/**
 * @brief 获取录音配置API处理函数
 */
int handle_recorder_get(struct MHD_Connection *connection,
                       const char *url,
                       const char *method,
                       const char *upload_data,
                       size_t *upload_data_size,
                       void **con_cls) {
    (void)url; (void)method; (void)upload_data; (void)upload_data_size; (void)con_cls;

    cfg_recorder_t config;
    cJSON *response_json = NULL;

    // 加载配置
    if (recorder_config_load(&config) != 0) {
        // 发送错误响应
        cJSON *error_json = cJSON_CreateObject();
        cJSON_AddStringToObject(error_json, "error", "Failed to load recorder configuration");
        char *error_str = cJSON_Print(error_json);

        struct MHD_Response *response = MHD_create_response_from_buffer(
            strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
        MHD_add_response_header(response, "Content-Type", "application/json");
        enum MHD_Result result = MHD_queue_response(connection, 500, response);

        free(error_str);
        cJSON_Delete(error_json);
        MHD_destroy_response(response);
        return (result == MHD_YES) ? 0 : -1;
    }

    // 转换为JSON
    if (recorder_config_to_json(&config, &response_json) != 0) {
        // 发送错误响应
        cJSON *error_json = cJSON_CreateObject();
        cJSON_AddStringToObject(error_json, "error", "Failed to convert recorder config to JSON");
        char *error_str = cJSON_Print(error_json);

        struct MHD_Response *response = MHD_create_response_from_buffer(
            strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
        MHD_add_response_header(response, "Content-Type", "application/json");
        enum MHD_Result result = MHD_queue_response(connection, 500, response);

        free(error_str);
        cJSON_Delete(error_json);
        MHD_destroy_response(response);
        return (result == MHD_YES) ? 0 : -1;
    }

    // 发送成功响应
    char *json_str = cJSON_Print(response_json);
    struct MHD_Response *response = MHD_create_response_from_buffer(
        strlen(json_str), json_str, MHD_RESPMEM_MUST_COPY);
    MHD_add_response_header(response, "Content-Type", "application/json");
    MHD_add_response_header(response, "Access-Control-Allow-Origin", "*");
    enum MHD_Result result = MHD_queue_response(connection, 200, response);

    free(json_str);
    cJSON_Delete(response_json);
    MHD_destroy_response(response);
    return (result == MHD_YES) ? 0 : -1;
}

/**
 * @brief 设置录音配置API处理函数
 */
int handle_recorder_post(struct MHD_Connection *connection,
                        const char *url,
                        const char *method,
                        const char *upload_data,
                        size_t *upload_data_size,
                        void **con_cls) {
    (void)url; (void)method;

    // 获取POST数据
    cJSON *request_data = api_request_get_json_data(connection, upload_data, upload_data_size, con_cls);
    if (!request_data) {
        if (*upload_data_size == 0) {
            // 发送错误响应
            cJSON *error_json = cJSON_CreateObject();
            cJSON_AddStringToObject(error_json, "error", "No JSON data provided");
            char *error_str = cJSON_Print(error_json);

            struct MHD_Response *response = MHD_create_response_from_buffer(
                strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
            MHD_add_response_header(response, "Content-Type", "application/json");
            enum MHD_Result result = MHD_queue_response(connection, 400, response);

            free(error_str);
            cJSON_Delete(error_json);
            MHD_destroy_response(response);
            return (result == MHD_YES) ? 0 : -1;
        }
        return MHD_YES; // 继续等待数据
    }

    cfg_recorder_t config;

    // JSON转配置结构
    if (recorder_json_to_config(request_data, &config) != 0) {
        cJSON_Delete(request_data);
        // 发送错误响应
        cJSON *error_json = cJSON_CreateObject();
        cJSON_AddStringToObject(error_json, "error", "Invalid recorder configuration data");
        char *error_str = cJSON_Print(error_json);

        struct MHD_Response *response = MHD_create_response_from_buffer(
            strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
        MHD_add_response_header(response, "Content-Type", "application/json");
        enum MHD_Result result = MHD_queue_response(connection, 400, response);

        free(error_str);
        cJSON_Delete(error_json);
        MHD_destroy_response(response);
        return (result == MHD_YES) ? 0 : -1;
    }

    // 保存配置
    if (recorder_config_save(&config) != 0) {
        cJSON_Delete(request_data);
        // 发送错误响应
        cJSON *error_json = cJSON_CreateObject();
        cJSON_AddStringToObject(error_json, "error", "Failed to save recorder configuration");
        char *error_str = cJSON_Print(error_json);

        struct MHD_Response *response = MHD_create_response_from_buffer(
            strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
        MHD_add_response_header(response, "Content-Type", "application/json");
        enum MHD_Result result = MHD_queue_response(connection, 500, response);

        free(error_str);
        cJSON_Delete(error_json);
        MHD_destroy_response(response);
        return (result == MHD_YES) ? 0 : -1;
    }

    // 返回成功响应
    cJSON *response_data = cJSON_CreateObject();
    cJSON_AddStringToObject(response_data, "message", "Recorder configuration saved successfully");
    char *json_str = cJSON_Print(response_data);

    struct MHD_Response *response = MHD_create_response_from_buffer(
        strlen(json_str), json_str, MHD_RESPMEM_MUST_COPY);
    MHD_add_response_header(response, "Content-Type", "application/json");
    MHD_add_response_header(response, "Access-Control-Allow-Origin", "*");
    enum MHD_Result result = MHD_queue_response(connection, 200, response);

    free(json_str);
    cJSON_Delete(response_data);
    cJSON_Delete(request_data);
    MHD_destroy_response(response);
    return (result == MHD_YES) ? 0 : -1;
}

/**
 * @brief 初始化录音配置模块
 */
int recorder_config_init(void) {
    // 初始化配置模块结构
    s_recorder_module.module_name = "recorder";
    s_recorder_module.config_file_path = RECORDER_CONFIG_FILE;
    s_recorder_module.config_struct_size = sizeof(cfg_recorder_t);
    s_recorder_module.load_config = recorder_module_load;
    s_recorder_module.save_config = recorder_module_save;
    s_recorder_module.validate_config = recorder_module_validate;
    s_recorder_module.config_to_json = recorder_module_to_json;
    s_recorder_module.json_to_config = recorder_module_from_json;

    // 注册到配置管理器
    if (config_manager_register(&s_recorder_module) != 0) {
        printf("Failed to register recorder config module\n");
        return -1;
    }

    printf("Recorder config module initialized\n");
    return 0;
}

/**
 * @brief 清理录音配置模块
 */
void recorder_config_cleanup(void) {
    printf("Recorder config module cleanup\n");
}
