/**
 * @file switch_config.c
 * @brief 交换机配置管理实现 - 对应旧项目0switch.c
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#include "switch_config.h"
#include "config_interface.h"
#include "global_config.h"
#include "file_utils.h"
#include "network_utils.h"
#include "api_router.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <arpa/inet.h>

// 静态变量（按命名规范）
static config_module_t s_switch_module;

/**
 * @brief 设置交换机配置默认值 - 严格对应旧项目stCfgConf+stCfgPeerBase
 */
void switch_config_set_default(cfg_switch_t *config) {
    if (!config) return;

    memset(config, 0, sizeof(cfg_switch_t));
    
    // 设置会议配置默认值
    config->conf.work_mode = 1;                        // 交换机模式
    config->conf.spec_function = 0;                    // 特殊功能关闭
    config->conf.peer_base_num = 0;                    // 默认无对等基站
    config->conf.voice_ip = inet_addr(SWITCH_DEFAULT_VOICE_IP); // 默认语音IP
    config->conf.vbus_base_port = SWITCH_DEFAULT_VOICE_PORT;    // 默认语音端口
    config->conf.vchan_number = SWITCH_DEFAULT_VCHAN_NUM;       // 默认通道数
    config->conf.buffertime = SWITCH_DEFAULT_BUFFER_TIME;       // 默认缓冲时间
    config->conf.downtime = SWITCH_DEFAULT_DOWN_TIME;           // 默认掉线时间
    
    // 初始化对等基站配置（默认为空）
    for (int i = 0; i < MAX_PEER_BASE_NUM; i++) {
        config->peers[i].peer_ip = inet_addr("***********");
        config->peers[i].peer_voice_ip = inet_addr("***********");
        config->peers[i].peer_data_listen_port = 2600;
        config->peers[i].peer_voice_port_base = 2800;
        config->peers[i].peer_net_address = (10 << 16) | (1 << 8) | 1; // DS=10, SW=1, BS=1
        config->peers[i].peer_type = 1; // 默认交换机类型
        
        // 初始化通道映射
        for (int j = 0; j < 12; j++) {
            config->peers[i].peer_vbus_to_chan[j] = j + 1;
        }
    }
}

/**
 * @brief 加载交换机配置
 */
int switch_config_load(cfg_switch_t *config) {
    if (!config) return -1;
    
    // 获取动态配置路径
    char config_path[512];
    if (global_config_get_path(SWITCH_CONFIG_FILE, config_path, sizeof(config_path)) != 0) {
        printf("Warning: Failed to get switch config path, using defaults\n");
        switch_config_set_default(config);
        return 0;
    }
    
    // 使用公共工具模块读取二进制配置文件
    if (file_utils_read_binary(config_path, 0, sizeof(cfg_switch_t), config) != 0) {
        printf("Warning: Failed to read switch config from %s, using defaults\n", config_path);
        switch_config_set_default(config);
        return 0; // 使用默认配置不算错误
    }
    
    return 0;
}

/**
 * @brief 保存交换机配置
 */
int switch_config_save(const cfg_switch_t *config) {
    if (!config) return -1;
    
    // 验证配置
    if (switch_config_validate(config) != 0) {
        return -1;
    }
    
    // 获取动态配置路径
    char config_path[512];
    if (global_config_get_path(SWITCH_CONFIG_FILE, config_path, sizeof(config_path)) != 0) {
        printf("Error: Failed to get switch config path for saving\n");
        return -1;
    }
    
    // 使用公共工具模块写入二进制配置文件
    return file_utils_write_binary(config_path, 0, sizeof(cfg_switch_t), config);
}

/**
 * @brief 验证交换机配置 - 对应旧项目stCfgConf+stCfgPeerBase验证逻辑
 */
int switch_config_validate(const cfg_switch_t *config) {
    if (!config) return -1;

    // 验证工作模式
    if (config->conf.work_mode != 1) {
        printf("Invalid work mode: %u\n", config->conf.work_mode);
        return -1;
    }

    // 验证对等基站数量
    if (config->conf.peer_base_num > MAX_PEER_BASE_NUM) {
        printf("Invalid peer base number: %d\n", config->conf.peer_base_num);
        return -1;
    }

    // 验证语音通道数
    if (config->conf.vchan_number < 1 || config->conf.vchan_number > 64) {
        printf("Invalid voice channel count: %d\n", config->conf.vchan_number);
        return -1;
    }

    // 验证语音端口
    if (config->conf.vbus_base_port == 0) {
        printf("Invalid voice port configuration\n");
        return -1;
    }

    // 验证对等基站配置
    for (int i = 0; i < config->conf.peer_base_num; i++) {
        // 验证对等类型
        if (config->peers[i].peer_type < 1 || config->peers[i].peer_type > 2) {
            printf("Invalid peer type: %d\n", config->peers[i].peer_type);
            return -1;
        }
        
        // 验证地址编码范围
        uint32_t ds = (config->peers[i].peer_net_address >> 16) & 0x3F;
        uint32_t sw = (config->peers[i].peer_net_address >> 8) & 0xFF;
        uint32_t bs = config->peers[i].peer_net_address & 0x1F;
        
        if (ds < 1 || ds > 63 || sw < 1 || sw > 255 || bs < 1 || bs > 31) {
            printf("Invalid peer network address: DS=%u, SW=%u, BS=%u\n", ds, sw, bs);
            return -1;
        }
    }

    return 0;
}

/**
 * @brief 交换机配置转JSON - 对应旧项目stCfgConf+stCfgPeerBase格式
 */
int switch_config_to_json(const cfg_switch_t *config, cJSON **json) {
    if (!config || !json) return -1;

    *json = cJSON_CreateObject();
    if (!*json) return -1;

    // 输出会议配置
    cJSON_AddNumberToObject(*json, "work_mode", config->conf.work_mode);
    cJSON_AddNumberToObject(*json, "spec_function", config->conf.spec_function);
    cJSON_AddNumberToObject(*json, "peer_base_num", config->conf.peer_base_num);
    
    // IP地址转换
    char ip_str[16];
    if (ip_utils_binary_to_string(config->conf.voice_ip, ip_str, sizeof(ip_str)) == 0) {
        cJSON_AddStringToObject(*json, "voice_ip", ip_str);
    }
    
    cJSON_AddNumberToObject(*json, "vbus_base_port", config->conf.vbus_base_port);
    cJSON_AddNumberToObject(*json, "vchan_number", config->conf.vchan_number);
    cJSON_AddNumberToObject(*json, "buffertime", config->conf.buffertime);
    cJSON_AddNumberToObject(*json, "downtime", config->conf.downtime);
    
    // 对等基站配置数组
    cJSON *peers_array = cJSON_CreateArray();
    for (int i = 0; i < config->conf.peer_base_num && i < MAX_PEER_BASE_NUM; i++) {
        cJSON *peer_obj = cJSON_CreateObject();
        
        // 对等IP地址转换
        if (ip_utils_binary_to_string(config->peers[i].peer_ip, ip_str, sizeof(ip_str)) == 0) {
            cJSON_AddStringToObject(peer_obj, "peer_ip", ip_str);
        }
        if (ip_utils_binary_to_string(config->peers[i].peer_voice_ip, ip_str, sizeof(ip_str)) == 0) {
            cJSON_AddStringToObject(peer_obj, "peer_voice_ip", ip_str);
        }
        
        cJSON_AddNumberToObject(peer_obj, "peer_data_listen_port", config->peers[i].peer_data_listen_port);
        cJSON_AddNumberToObject(peer_obj, "peer_voice_port_base", config->peers[i].peer_voice_port_base);
        cJSON_AddNumberToObject(peer_obj, "peer_net_address", config->peers[i].peer_net_address);
        
        // 分解地址编码便于前端显示
        cJSON_AddNumberToObject(peer_obj, "peer_ds", (config->peers[i].peer_net_address >> 16) & 0x3F);
        cJSON_AddNumberToObject(peer_obj, "peer_sw", (config->peers[i].peer_net_address >> 8) & 0xFF);
        cJSON_AddNumberToObject(peer_obj, "peer_bs", config->peers[i].peer_net_address & 0x1F);
        
        cJSON_AddNumberToObject(peer_obj, "peer_type", config->peers[i].peer_type);
        
        // 通道映射数组
        cJSON *chan_array = cJSON_CreateArray();
        for (int j = 0; j < 12; j++) {
            cJSON_AddItemToArray(chan_array, cJSON_CreateNumber(config->peers[i].peer_vbus_to_chan[j]));
        }
        cJSON_AddItemToObject(peer_obj, "peer_vbus_to_chan", chan_array);
        
        cJSON_AddItemToArray(peers_array, peer_obj);
    }
    cJSON_AddItemToObject(*json, "peers", peers_array);
    
    return 0;
}

/**
 * @brief JSON转交换机配置 - 对应旧项目stCfgConf+stCfgPeerBase格式
 */
int switch_json_to_config(const cJSON *json, cfg_switch_t *config) {
    if (!json || !config) return -1;

    // 先设置默认值
    switch_config_set_default(config);

    cJSON *item;

    // 解析会议配置
    item = cJSON_GetObjectItem(json, "work_mode");
    if (item && cJSON_IsNumber(item)) {
        config->conf.work_mode = (uint32_t)item->valueint;
    }

    item = cJSON_GetObjectItem(json, "spec_function");
    if (item && cJSON_IsNumber(item)) {
        config->conf.spec_function = (uint16_t)item->valueint;
    }

    item = cJSON_GetObjectItem(json, "peer_base_num");
    if (item && cJSON_IsNumber(item)) {
        int peer_num = item->valueint;
        if (peer_num >= 0 && peer_num <= MAX_PEER_BASE_NUM) {
            config->conf.peer_base_num = (uint8_t)peer_num;
        }
    }

    item = cJSON_GetObjectItem(json, "voice_ip");
    if (item && cJSON_IsString(item)) {
        uint32_t temp_ip;
        if (ip_utils_string_to_binary(item->valuestring, &temp_ip) == 0) {
            config->conf.voice_ip = temp_ip;
        }
    }

    item = cJSON_GetObjectItem(json, "vbus_base_port");
    if (item && cJSON_IsNumber(item)) {
        config->conf.vbus_base_port = (uint16_t)item->valueint;
    }

    item = cJSON_GetObjectItem(json, "vchan_number");
    if (item && cJSON_IsNumber(item)) {
        config->conf.vchan_number = (uint8_t)item->valueint;
    }

    item = cJSON_GetObjectItem(json, "buffertime");
    if (item && cJSON_IsNumber(item)) {
        config->conf.buffertime = (uint16_t)item->valueint;
    }

    item = cJSON_GetObjectItem(json, "downtime");
    if (item && cJSON_IsNumber(item)) {
        config->conf.downtime = (uint16_t)item->valueint;
    }

    // 解析对等基站配置数组
    cJSON *peers_array = cJSON_GetObjectItem(json, "peers");
    if (peers_array && cJSON_IsArray(peers_array)) {
        int array_size = cJSON_GetArraySize(peers_array);
        int max_peers = (array_size < config->conf.peer_base_num) ? array_size : config->conf.peer_base_num;
        
        for (int i = 0; i < max_peers && i < MAX_PEER_BASE_NUM; i++) {
            cJSON *peer_obj = cJSON_GetArrayItem(peers_array, i);
            if (!peer_obj || !cJSON_IsObject(peer_obj)) continue;
            
            // 解析对等IP地址
            cJSON *peer_ip = cJSON_GetObjectItem(peer_obj, "peer_ip");
            if (peer_ip && cJSON_IsString(peer_ip)) {
                uint32_t temp_ip;
                if (ip_utils_string_to_binary(peer_ip->valuestring, &temp_ip) == 0) {
                    config->peers[i].peer_ip = temp_ip;
                }
            }
            
            cJSON *peer_voice_ip = cJSON_GetObjectItem(peer_obj, "peer_voice_ip");
            if (peer_voice_ip && cJSON_IsString(peer_voice_ip)) {
                uint32_t temp_ip;
                if (ip_utils_string_to_binary(peer_voice_ip->valuestring, &temp_ip) == 0) {
                    config->peers[i].peer_voice_ip = temp_ip;
                }
            }
            
            // 解析其他字段
            cJSON *peer_data_port = cJSON_GetObjectItem(peer_obj, "peer_data_listen_port");
            if (peer_data_port && cJSON_IsNumber(peer_data_port)) {
                config->peers[i].peer_data_listen_port = (uint16_t)peer_data_port->valueint;
            }
            
            cJSON *peer_voice_port = cJSON_GetObjectItem(peer_obj, "peer_voice_port_base");
            if (peer_voice_port && cJSON_IsNumber(peer_voice_port)) {
                config->peers[i].peer_voice_port_base = (uint16_t)peer_voice_port->valueint;
            }
            
            // 处理地址编码（DS/SW/BS）
            cJSON *peer_ds = cJSON_GetObjectItem(peer_obj, "peer_ds");
            cJSON *peer_sw = cJSON_GetObjectItem(peer_obj, "peer_sw");
            cJSON *peer_bs = cJSON_GetObjectItem(peer_obj, "peer_bs");
            if (peer_ds && peer_sw && peer_bs) {
                uint32_t ds = (uint32_t)peer_ds->valueint & 0x3F;
                uint32_t sw = (uint32_t)peer_sw->valueint & 0xFF;
                uint32_t bs = (uint32_t)peer_bs->valueint & 0x1F;
                config->peers[i].peer_net_address = (ds << 16) | (sw << 8) | bs;
            }
            
            cJSON *peer_type = cJSON_GetObjectItem(peer_obj, "peer_type");
            if (peer_type && cJSON_IsNumber(peer_type)) {
                config->peers[i].peer_type = (uint8_t)peer_type->valueint;
            }
        }
    }

    return 0;
}

// 配置管理器接口函数（按命名规范）
int switch_module_load(void *config) {
    return switch_config_load((cfg_switch_t *)config);
}

int switch_module_save(const void *config) {
    return switch_config_save((const cfg_switch_t *)config);
}

int switch_module_validate(const void *config) {
    return switch_config_validate((const cfg_switch_t *)config);
}

int switch_module_to_json(const void *config, cJSON **json) {
    return switch_config_to_json((const cfg_switch_t *)config, json);
}

int switch_module_from_json(const cJSON *json, void *config) {
    return switch_json_to_config(json, (cfg_switch_t *)config);
}

// API处理函数（按命名规范）
/**
 * @brief 获取交换机配置API处理函数
 */
int handle_switch_get(struct MHD_Connection *connection,
                     const char *url,
                     const char *method,
                     const char *upload_data,
                     size_t *upload_data_size,
                     void **con_cls) {
    cfg_switch_t config;
    cJSON *json_response = NULL;
    char *json_string = NULL;
    struct MHD_Response *response = NULL;

    // 加载配置
    if (switch_config_load(&config) != 0) {
        const char *error_msg = "{\"error\":\"Failed to load switch config\"}";
        response = MHD_create_response_from_buffer(strlen(error_msg),
                                                  (void*)error_msg,
                                                  MHD_RESPMEM_PERSISTENT);
        if (response) {
            MHD_add_response_header(response, "Content-Type", "application/json");
            MHD_queue_response(connection, 500, response);
            MHD_destroy_response(response);
            return -1;
        }
        return -1;
    }

    // 转换为JSON
    if (switch_config_to_json(&config, &json_response) != 0) {
        const char *error_msg = "{\"error\":\"Failed to convert config to JSON\"}";
        response = MHD_create_response_from_buffer(strlen(error_msg),
                                                  (void*)error_msg,
                                                  MHD_RESPMEM_PERSISTENT);
        if (response) {
            MHD_add_response_header(response, "Content-Type", "application/json");
            MHD_queue_response(connection, 500, response);
            MHD_destroy_response(response);
            return -1;
        }
        return -1;
    }

    // 转换JSON为字符串
    json_string = cJSON_Print(json_response);
    cJSON_Delete(json_response);

    if (!json_string) {
        const char *error_msg = "{\"error\":\"Failed to serialize JSON\"}";
        response = MHD_create_response_from_buffer(strlen(error_msg),
                                                  (void*)error_msg,
                                                  MHD_RESPMEM_PERSISTENT);
        if (response) {
            MHD_add_response_header(response, "Content-Type", "application/json");
            MHD_queue_response(connection, 500, response);
            MHD_destroy_response(response);
            return -1;
        }
        return -1;
    }

    // 创建响应
    response = MHD_create_response_from_buffer(strlen(json_string),
                                              json_string,
                                              MHD_RESPMEM_MUST_FREE);
    if (response) {
        MHD_add_response_header(response, "Content-Type", "application/json");
        MHD_queue_response(connection, 200, response);
        MHD_destroy_response(response);
        return 0; // 成功
    } else {
        free(json_string);
        return -1; // 失败
    }
}

/**
 * @brief 设置交换机配置API处理函数
 */
int handle_switch_post(struct MHD_Connection *connection,
                      const char *url,
                      const char *method,
                      const char *upload_data,
                      size_t *upload_data_size,
                      void **con_cls) {
    cfg_switch_t config;
    cJSON *request_json = NULL;
    struct MHD_Response *response = NULL;

    // 获取POST数据
    request_json = api_request_get_json_data(connection, upload_data, upload_data_size, con_cls);
    if (!request_json) {
        // 如果数据还在接收中，返回继续处理
        return 0; // 继续处理
    }

    // JSON转配置结构
    if (switch_json_to_config(request_json, &config) != 0) {
        cJSON_Delete(request_json);
        const char *error_msg = "{\"error\":\"Invalid config data\"}";
        response = MHD_create_response_from_buffer(strlen(error_msg),
                                                  (void*)error_msg,
                                                  MHD_RESPMEM_PERSISTENT);
        if (response) {
            MHD_add_response_header(response, "Content-Type", "application/json");
            MHD_queue_response(connection, 400, response);
            MHD_destroy_response(response);
        }
        return -1;
    }

    cJSON_Delete(request_json);

    // 保存配置
    if (switch_config_save(&config) != 0) {
        const char *error_msg = "{\"error\":\"Failed to save switch config\"}";
        response = MHD_create_response_from_buffer(strlen(error_msg),
                                                  (void*)error_msg,
                                                  MHD_RESPMEM_PERSISTENT);
        if (response) {
            MHD_add_response_header(response, "Content-Type", "application/json");
            MHD_queue_response(connection, 500, response);
            MHD_destroy_response(response);
        }
        return -1;
    }

    // 成功响应
    const char *success_msg = "{\"status\":\"success\"}";
    response = MHD_create_response_from_buffer(strlen(success_msg),
                                              (void*)success_msg,
                                              MHD_RESPMEM_PERSISTENT);
    if (response) {
        MHD_add_response_header(response, "Content-Type", "application/json");
        MHD_queue_response(connection, 200, response);
        MHD_destroy_response(response);
        return 0; // 成功
    }

    return -1; // 失败
}

/**
 * @brief 初始化交换机配置模块
 */
int switch_config_init(void) {
    // 初始化配置模块结构
    s_switch_module.module_name = "switch";
    s_switch_module.config_file_path = SWITCH_CONFIG_FILE;
    s_switch_module.config_struct_size = sizeof(cfg_switch_t);
    s_switch_module.load_config = switch_module_load;
    s_switch_module.save_config = switch_module_save;
    s_switch_module.validate_config = switch_module_validate;
    s_switch_module.config_to_json = switch_module_to_json;
    s_switch_module.json_to_config = switch_module_from_json;

    // 注册到配置管理器
    if (config_manager_register(&s_switch_module) != 0) {
        printf("Failed to register switch config module\n");
        return -1;
    }

    printf("Switch config module initialized\n");
    return 0;
}

/**
 * @brief 清理交换机配置模块
 */
void switch_config_cleanup(void) {
    printf("Switch config module cleanup\n");
}
