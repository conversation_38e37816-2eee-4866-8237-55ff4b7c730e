/**
 * @file password_reader.c
 * @brief 密码文件读取器实现
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#include "password_reader.h"
#include "global_config.h"
#include "auth_handler.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

/**
 * @brief 从密码文件读取用户密码
 * @param username 用户名
 * @param password_hash 输出的密码哈希
 * @param hash_size 哈希缓冲区大小
 * @return 0成功，-1失败
 */
int password_reader_get_user_hash(const char *username, char *password_hash, size_t hash_size) {
    if (!username || !password_hash || hash_size == 0) {
        return -1;
    }

    // 获取密码文件路径
    char passwd_path[512];
    if (global_config_get_auth_path(AUTH_PASSWD_FILE, passwd_path, sizeof(passwd_path)) != 0) {
        printf("Error: Failed to get password file path\n");
        return -1;
    }

    FILE *fp = fopen(passwd_path, "r");
    if (!fp) {
        printf("Warning: Password file %s not found, using default authentication\n", passwd_path);
        return -1;
    }

    char line[256];
    char file_username[64];
    char file_password_hash[128];
    int found = 0;

    while (fgets(line, sizeof(line), fp)) {
        // 跳过注释和空行
        if (line[0] == '#' || line[0] == '\n' || line[0] == '\r') {
            continue;
        }

        // 解析用户名:密码哈希格式
        if (sscanf(line, "%63[^:]:%127s", file_username, file_password_hash) == 2) {
            if (strcmp(username, file_username) == 0) {
                strncpy(password_hash, file_password_hash, hash_size - 1);
                password_hash[hash_size - 1] = '\0';
                found = 1;
                break;
            }
        }
    }

    fclose(fp);
    return found ? 0 : -1;
}

/**
 * @brief 验证用户密码
 * @param username 用户名
 * @param password 明文密码
 * @return 0验证成功，-1验证失败
 */
int password_reader_verify_user(const char *username, const char *password) {
    if (!username || !password) {
        return -1;
    }

    // 尝试从密码文件读取
    char stored_hash[128];
    if (password_reader_get_user_hash(username, stored_hash, sizeof(stored_hash)) == 0) {
        // 如果文件中有密码记录，验证密码
        // 这里需要实现密码哈希验证逻辑
        // 由于旧项目使用简单的MD5+Base64，这里简化处理
        
        // 生成输入密码的哈希
        struct MD5Context mc;
        unsigned char final[16];
        char input_hash[64];
        
        MD5Init(&mc);
        MD5Update(&mc, (unsigned char *)password, strlen(password));
        MD5Final(final, &mc);
        
        strcpy(input_hash, "$1$");
        base64encode(final, input_hash + 3, 16);
        
        // 比较哈希值
        return strcmp(stored_hash, input_hash) == 0 ? 0 : -1;
    } else {
        // 如果密码文件不存在或没有找到用户，使用默认认证
        // 保持向后兼容性
        if (strcmp(username, "admin") == 0 && strcmp(password, "admin") == 0) {
            return 0;
        }
        return -1;
    }
}

/**
 * @brief 检查密码文件是否存在
 * @return 1存在，0不存在
 */
int password_reader_file_exists(void) {
    char passwd_path[512];
    if (global_config_get_auth_path(AUTH_PASSWD_FILE, passwd_path, sizeof(passwd_path)) != 0) {
        return 0;
    }

    FILE *fp = fopen(passwd_path, "r");
    if (fp) {
        fclose(fp);
        return 1;
    }
    return 0;
} 