/**
 * @file network_utils.c
 * @brief 网络操作工具模块实现
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#include "network_utils.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <arpa/inet.h>
#include <ctype.h>

/**
 * @brief IP字符串转二进制
 */
int ip_utils_string_to_binary(const char *ip_str, uint32_t *ip_binary) {
    if (!ip_str || !ip_binary) {
        return -1;
    }

    struct in_addr addr;
    if (inet_aton(ip_str, &addr) == 0) {
        return -1;
    }

    *ip_binary = addr.s_addr;
    return 0;
}

/**
 * @brief IP二进制转字符串
 */
int ip_utils_binary_to_string(uint32_t ip_binary, char *ip_str, size_t size) {
    if (!ip_str || size < 16) {
        return -1;
    }

    struct in_addr addr;
    addr.s_addr = ip_binary;
    
    const char *result = inet_ntoa(addr);
    if (!result) {
        return -1;
    }

    strncpy(ip_str, result, size - 1);
    ip_str[size - 1] = '\0';
    return 0;
}

/**
 * @brief 验证IP地址格式
 */
int ip_utils_validate_address(const char *ip_str) {
    if (!ip_str) {
        return -1;
    }

    struct in_addr addr;
    return inet_aton(ip_str, &addr) ? 0 : -1;
}

/**
 * @brief 验证子网掩码格式
 */
int ip_utils_validate_mask(const char *mask_str) {
    if (!mask_str) {
        return -1;
    }

    uint32_t mask;
    if (ip_utils_string_to_binary(mask_str, &mask) != 0) {
        return -1;
    }

    // 检查掩码是否连续
    mask = ntohl(mask);
    uint32_t inverted = ~mask;
    
    // 有效的子网掩码的反码应该是连续的1（从右边开始）
    return ((inverted & (inverted + 1)) == 0) ? 0 : -1;
}

/**
 * @brief 验证MAC地址格式
 */
int ip_utils_validate_mac(const char *mac_str) {
    if (!mac_str) {
        return -1;
    }

    int len = strlen(mac_str);
    if (len != 17) { // XX:XX:XX:XX:XX:XX
        return -1;
    }

    for (int i = 0; i < len; i++) {
        if (i % 3 == 2) {
            if (mac_str[i] != ':') {
                return -1;
            }
        } else {
            if (!isxdigit(mac_str[i])) {
                return -1;
            }
        }
    }

    return 0;
}

/**
 * @brief 检查IP地址是否在同一网段
 */
int ip_utils_same_subnet(uint32_t ip1, uint32_t ip2, uint32_t mask) {
    return (ip1 & mask) == (ip2 & mask) ? 1 : 0;
}

/**
 * @brief 获取网络地址
 */
uint32_t ip_utils_get_network(uint32_t ip, uint32_t mask) {
    return ip & mask;
}

/**
 * @brief 获取广播地址
 */
uint32_t ip_utils_get_broadcast(uint32_t ip, uint32_t mask) {
    return ip | (~mask);
}

/**
 * @brief MAC地址字符串转二进制
 */
int mac_utils_string_to_binary(const char *mac_str, uint8_t mac_binary[6]) {
    if (!mac_str || !mac_binary) {
        return -1;
    }

    if (ip_utils_validate_mac(mac_str) != 0) {
        return -1;
    }

    int values[6];
    if (sscanf(mac_str, "%x:%x:%x:%x:%x:%x",
               &values[0], &values[1], &values[2],
               &values[3], &values[4], &values[5]) != 6) {
        return -1;
    }

    for (int i = 0; i < 6; i++) {
        mac_binary[i] = (uint8_t)values[i];
    }

    return 0;
}

/**
 * @brief MAC地址二进制转字符串
 */
int mac_utils_binary_to_string(const uint8_t mac_binary[6], char *mac_str, size_t size) {
    if (!mac_binary || !mac_str || size < 18) {
        return -1;
    }

    snprintf(mac_str, size, "%02x:%02x:%02x:%02x:%02x:%02x",
             mac_binary[0], mac_binary[1], mac_binary[2],
             mac_binary[3], mac_binary[4], mac_binary[5]);

    return 0;
}
