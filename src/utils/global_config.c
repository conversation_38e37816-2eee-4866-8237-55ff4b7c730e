/**
 * @file global_config.c
 * @brief 全局配置管理模块实现
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#include "global_config.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>

// 全局配置实例
static global_config_t g_global_config = {0};

/**
 * @brief 初始化全局配置
 */
int global_config_init(const char *config_file) {
    if (g_global_config.initialized) {
        return 0; // 已经初始化
    }

    // 设置默认值
    snprintf(g_global_config.config_dir, sizeof(g_global_config.config_dir), "%s", DEFAULT_CONFIG_DIR);
    snprintf(g_global_config.network_config_dir, sizeof(g_global_config.network_config_dir), "%s", DEFAULT_NETWORK_CONFIG_DIR);
    snprintf(g_global_config.backup_config_dir, sizeof(g_global_config.backup_config_dir), "%s", DEFAULT_BACKUP_CONFIG_DIR);
    snprintf(g_global_config.log_dir, sizeof(g_global_config.log_dir), "%s", DEFAULT_LOG_DIR);
    snprintf(g_global_config.auth_dir, sizeof(g_global_config.auth_dir), "%s", "/etc/boa");

    // 如果指定了配置文件，则从文件加载
    if (config_file && strlen(config_file) > 0) {
        if (global_config_load_from_file(config_file) != 0) {
            printf("Warning: Failed to load config from file %s, using defaults\n", config_file);
        }
    }

    g_global_config.initialized = 1;
    printf("Global config initialized:\n");
    printf("  Config dir: %s\n", g_global_config.config_dir);
    printf("  Network config dir: %s\n", g_global_config.network_config_dir);
    printf("  Auth dir: %s\n", g_global_config.auth_dir);
    
    return 0;
}

/**
 * @brief 获取全局配置实例
 */
global_config_t *global_config_get(void) {
    if (!g_global_config.initialized) {
        global_config_init(NULL);
    }
    return &g_global_config;
}

/**
 * @brief 获取配置文件完整路径
 */
int global_config_get_path(const char *config_name, char *full_path, size_t path_size) {
    if (!config_name || !full_path || path_size == 0) {
        return -1;
    }

    global_config_t *config = global_config_get();
    int ret = snprintf(full_path, path_size, "%s/%s", config->config_dir, config_name);
    
    if (ret < 0 || (size_t)ret >= path_size) {
        return -1; // 缓冲区不足
    }
    
    return 0;
}

/**
 * @brief 获取网络配置文件完整路径
 */
int global_config_get_network_path(const char *config_name, char *full_path, size_t path_size) {
    if (!config_name || !full_path || path_size == 0) {
        return -1;
    }

    global_config_t *config = global_config_get();
    int ret = snprintf(full_path, path_size, "%s/%s", config->network_config_dir, config_name);
    
    if (ret < 0 || (size_t)ret >= path_size) {
        return -1; // 缓冲区不足
    }
    
    return 0;
}

/**
 * @brief 获取认证文件完整路径
 */
int global_config_get_auth_path(const char *config_name, char *full_path, size_t path_size) {
    if (!config_name || !full_path || path_size == 0) {
        return -1;
    }

    global_config_t *config = global_config_get();
    int ret = snprintf(full_path, path_size, "%s/%s", config->auth_dir, config_name);
    
    if (ret < 0 || (size_t)ret >= path_size) {
        return -1; // 缓冲区不足
    }
    
    return 0;
}

/**
 * @brief 设置配置目录
 */
int global_config_set_config_dir(const char *config_dir) {
    if (!config_dir) {
        return -1;
    }

    global_config_t *config = global_config_get();
    snprintf(config->config_dir, sizeof(config->config_dir), "%s", config_dir);
    
    return 0;
}

/**
 * @brief 设置网络配置目录
 */
int global_config_set_network_config_dir(const char *network_config_dir) {
    if (!network_config_dir) {
        return -1;
    }

    global_config_t *config = global_config_get();
    snprintf(config->network_config_dir, sizeof(config->network_config_dir), "%s", network_config_dir);
    
    return 0;
}

/**
 * @brief 设置认证文件目录
 */
int global_config_set_auth_dir(const char *auth_dir) {
    if (!auth_dir) {
        return -1;
    }

    global_config_t *config = global_config_get();
    snprintf(config->auth_dir, sizeof(config->auth_dir), "%s", auth_dir);
    
    return 0;
}

/**
 * @brief 从配置文件加载目录设置
 */
int global_config_load_from_file(const char *config_file) {
    if (!config_file) {
        return -1;
    }

    FILE *fp = fopen(config_file, "r");
    if (!fp) {
        return -1;
    }

    char line[512];
    char key[128];
    char value[256];
    
    while (fgets(line, sizeof(line), fp)) {
        // 跳过注释和空行
        if (line[0] == '#' || line[0] == '\n' || line[0] == '\r') {
            continue;
        }

        // 解析键值对
        if (sscanf(line, "%127[^=]=%255s", key, value) == 2) {
            // 去除键值两端的空白字符
            char *key_start = key;
            char *key_end = key + strlen(key) - 1;
            while (*key_start == ' ' || *key_start == '\t') key_start++;
            while (key_end > key_start && (*key_end == ' ' || *key_end == '\t' || *key_end == '\n' || *key_end == '\r')) {
                *key_end = '\0';
                key_end--;
            }

            char *value_start = value;
            char *value_end = value + strlen(value) - 1;
            while (*value_start == ' ' || *value_start == '\t') value_start++;
            while (value_end > value_start && (*value_end == ' ' || *value_end == '\t' || *value_end == '\n' || *value_end == '\r')) {
                *value_end = '\0';
                value_end--;
            }

            // 设置配置值
            if (strcmp(key_start, "config_dir") == 0) {
                global_config_set_config_dir(value_start);
            } else if (strcmp(key_start, "network_config_dir") == 0) {
                global_config_set_network_config_dir(value_start);
            } else if (strcmp(key_start, "auth_dir") == 0) {
                global_config_set_auth_dir(value_start);
            } else if (strcmp(key_start, "backup_config_dir") == 0) {
                snprintf(g_global_config.backup_config_dir, sizeof(g_global_config.backup_config_dir), "%s", value_start);
            } else if (strcmp(key_start, "log_dir") == 0) {
                snprintf(g_global_config.log_dir, sizeof(g_global_config.log_dir), "%s", value_start);
            }
        }
    }

    fclose(fp);
    return 0;
}

/**
 * @brief 清理全局配置
 */
void global_config_cleanup(void) {
    memset(&g_global_config, 0, sizeof(g_global_config));
} 