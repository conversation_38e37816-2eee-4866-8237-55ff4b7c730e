# utils模块CMake配置

# 设置源文件
set(UTILS_SOURCES
    file_utils.c
    global_config.c
    network_utils.c
    password_reader.c
)

# 添加包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/../..
    ${CMAKE_CURRENT_SOURCE_DIR}/..
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# 创建静态库
add_library(webcfg_utils STATIC ${UTILS_SOURCES})

# 继承根CMakeLists.txt的编译选项
target_compile_options(webcfg_utils PRIVATE
    -Wall
    -Wextra
    -Werror
    -Wno-unused-parameter
    $<$<CONFIG:Debug>:-g -O0>
    $<$<CONFIG:Release>:-O2 -DNDEBUG>
)

# 设置目标属性
set_target_properties(webcfg_utils PROPERTIES
    POSITION_INDEPENDENT_CODE ON
)