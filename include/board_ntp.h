/**
 * @file board_ntp.h
 * @brief NTP时间同步配置管理接口 - 对应旧项目0ntp.c
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#ifndef board_ntp_H
#define board_ntp_H

#include <stdint.h>
#include <cjson/cJSON.h>
#include <microhttpd.h>

// NTP模块默认配置值（对应旧项目默认值）
#define NTP_DEFAULT_SERVER      "pool.ntp.org"

/**
 * @brief NTP配置结构体 - 严格对应旧项目功能
 */
typedef struct {
    char    ntp_server[50];     // NTP服务器地址（对应NtpAddr，限制50字符）
    uint8_t enable_client;      // 作为NTP客户端（对应UseNtp）
    uint8_t enable_server;      // 作为NTP服务器（对应NtpServ）
    uint8_t reserved[13];       // 保留字段
} cfg_ntp_t;

/**
 * @brief 设置NTP配置默认值
 * @param config NTP配置结构指针
 */
void board_ntp_set_default(cfg_ntp_t *config);

/**
 * @brief 加载NTP配置
 * @param config NTP配置结构指针
 * @return 0成功，-1失败
 */
int board_ntp_load(cfg_ntp_t *config);

/**
 * @brief 保存NTP配置
 * @param config NTP配置结构指针
 * @return 0成功，-1失败
 */
int board_ntp_save(const cfg_ntp_t *config);

/**
 * @brief 验证NTP配置
 * @param config NTP配置结构指针
 * @return 0有效，-1无效
 */
int board_ntp_validate(const cfg_ntp_t *config);

/**
 * @brief NTP配置转JSON
 * @param config NTP配置结构指针
 * @param json JSON对象指针的指针
 * @return 0成功，-1失败
 */
int board_ntp_to_json(const cfg_ntp_t *config, cJSON **json);

/**
 * @brief JSON转NTP配置
 * @param json JSON对象指针
 * @param config NTP配置结构指针
 * @return 0成功，-1失败
 */
int ntp_json_to_config(const cJSON *json, cfg_ntp_t *config);

// 配置管理器接口函数（按命名规范）
int ntp_module_load(void *config);
int ntp_module_save(const void *config);
int ntp_module_validate(const void *config);
int ntp_module_to_json(const void *config, cJSON **json);
int ntp_module_from_json(const cJSON *json, void *config);

// API处理函数（按命名规范）
/**
 * @brief 获取NTP配置API处理函数
 * @param connection HTTP连接
 * @param url 请求URL
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小
 * @param con_cls 连接状态
 * @return 0成功，-1失败
 */
int handle_board_ntp_get(struct MHD_Connection *connection,
                  const char *url,
                  const char *method,
                  const char *upload_data,
                  size_t *upload_data_size,
                  void **con_cls);

/**
 * @brief 设置NTP配置API处理函数
 * @param connection HTTP连接
 * @param url 请求URL
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小
 * @param con_cls 连接状态
 * @return 0成功，-1失败
 */
int handle_board_ntp_post(struct MHD_Connection *connection,
                   const char *url,
                   const char *method,
                   const char *upload_data,
                   size_t *upload_data_size,
                   void **con_cls);

/**
 * @brief 初始化NTP配置模块
 * @return 0成功，-1失败
 */
int board_ntp_init(void);

/**
 * @brief 清理NTP配置模块
 */
void board_ntp_cleanup(void);

#endif // board_ntp_H
