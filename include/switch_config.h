/**
 * @file switch_config.h
 * @brief 交换机配置管理接口 - 对应旧项目0switch.c
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#ifndef SWITCH_CONFIG_H
#define SWITCH_CONFIG_H

#include <stdint.h>
#include <cjson/cJSON.h>
#include <microhttpd.h>

// 交换机模块默认配置值（对应旧项目）
#define SWITCH_DEFAULT_VOICE_IP         "*************"
#define SWITCH_DEFAULT_VOICE_PORT       2800
#define SWITCH_DEFAULT_VCHAN_NUM        16
#define SWITCH_DEFAULT_BUFFER_TIME      100  // 毫秒
#define SWITCH_DEFAULT_DOWN_TIME        30   // 秒
#define MAX_PEER_BASE_NUM              16

/**
 * @brief 交换机会议配置结构体 - 严格对应旧项目stCfgConf
 */
typedef struct {
    uint32_t    work_mode;      // 工作模式（read only，固定为1）
    uint16_t    spec_function;  // 特殊功能标志
    uint8_t     peer_base_num;  // 对等基站数量
    uint32_t    voice_ip;       // 语音IP地址
    uint16_t    vbus_base_port; // 语音总线基础端口
    uint8_t     vchan_number;   // 语音通道数量
    uint16_t    buffertime;     // 缓冲时间（毫秒）
    uint16_t    downtime;       // 掉线时间（秒）
    uint8_t     reserved[1];    // 保留字段
} cfg_switch_conf_t;

/**
 * @brief 对等基站配置结构体 - 严格对应旧项目stCfgPeerBase
 */
typedef struct {
    uint32_t    peer_ip;                // 对等IP地址
    uint32_t    peer_voice_ip;          // 对等语音IP地址
    uint16_t    peer_data_listen_port;  // 对等数据监听端口
    uint16_t    peer_voice_port_base;   // 对等语音端口基址
    uint32_t    peer_net_address;       // 对等网络地址（24位，使用低24位）
    uint8_t     peer_type;              // 对等类型（1-交换机，2-基站）
    uint8_t     peer_vbus_to_chan[12];  // 对等语音总线到通道映射
    uint8_t     reserved[1];            // 保留字段
} cfg_switch_peer_t;

/**
 * @brief 交换功能完整配置结构体
 */
typedef struct {
    cfg_switch_conf_t conf;                         // 会议配置
    cfg_switch_peer_t peers[MAX_PEER_BASE_NUM];     // 对等基站配置数组
} cfg_switch_t;

/**
 * @brief 设置交换机配置默认值
 * @param config 交换机配置结构指针
 */
void switch_config_set_default(cfg_switch_t *config);

/**
 * @brief 加载交换机配置
 * @param config 交换机配置结构指针
 * @return 0成功，-1失败
 */
int switch_config_load(cfg_switch_t *config);

/**
 * @brief 保存交换机配置
 * @param config 交换机配置结构指针
 * @return 0成功，-1失败
 */
int switch_config_save(const cfg_switch_t *config);

/**
 * @brief 验证交换机配置
 * @param config 交换机配置结构指针
 * @return 0有效，-1无效
 */
int switch_config_validate(const cfg_switch_t *config);

/**
 * @brief 交换机配置转JSON
 * @param config 交换机配置结构指针
 * @param json JSON对象指针的指针
 * @return 0成功，-1失败
 */
int switch_config_to_json(const cfg_switch_t *config, cJSON **json);

/**
 * @brief JSON转交换机配置
 * @param json JSON对象指针
 * @param config 交换机配置结构指针
 * @return 0成功，-1失败
 */
int switch_json_to_config(const cJSON *json, cfg_switch_t *config);

// 配置管理器接口函数（按命名规范）
int switch_module_load(void *config);
int switch_module_save(const void *config);
int switch_module_validate(const void *config);
int switch_module_to_json(const void *config, cJSON **json);
int switch_module_from_json(const cJSON *json, void *config);

// API处理函数（按命名规范）
/**
 * @brief 获取交换机配置API处理函数
 * @param connection HTTP连接
 * @param url 请求URL
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小
 * @param con_cls 连接状态
 * @return 0成功，-1失败
 */
int handle_switch_get(struct MHD_Connection *connection,
                     const char *url,
                     const char *method,
                     const char *upload_data,
                     size_t *upload_data_size,
                     void **con_cls);

/**
 * @brief 设置交换机配置API处理函数
 * @param connection HTTP连接
 * @param url 请求URL
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小
 * @param con_cls 连接状态
 * @return 0成功，-1失败
 */
int handle_switch_post(struct MHD_Connection *connection,
                      const char *url,
                      const char *method,
                      const char *upload_data,
                      size_t *upload_data_size,
                      void **con_cls);

/**
 * @brief 初始化交换机配置模块
 * @return 0成功，-1失败
 */
int switch_config_init(void);

/**
 * @brief 清理交换机配置模块
 */
void switch_config_cleanup(void);

#endif // SWITCH_CONFIG_H
