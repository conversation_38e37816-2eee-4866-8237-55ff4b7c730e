/**
 * @file global_config.h
 * @brief 全局配置管理模块 - 支持运行时配置目录设置
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#ifndef GLOBAL_CONFIG_H
#define GLOBAL_CONFIG_H

#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

// 默认配置目录定义
#define DEFAULT_CONFIG_DIR              "/home/<USER>/cfg"
#define DEFAULT_NETWORK_CONFIG_DIR      "/etc"
#define DEFAULT_BACKUP_CONFIG_DIR       "/home/<USER>/cfg/default"
#define DEFAULT_LOG_DIR                 "/var/log"

// 配置文件名定义
#define NETWORK_SETTING_FILE            "network-setting"
#define ETHERNET_SETTING_FILE           "eth0-setting"
#define WLAN_SETTING_FILE               "wlan0-setting"
#define WWAN_SETTING_FILE               "3g-setting"
#define CENTER_CONFIG_FILE              "callcenter.cfg"
#define GATEWAY_CONFIG_FILE             "gateway.cfg"
#define RECORDER_CONFIG_FILE            "record.cfg"
#define SCI_CONFIG_FILE                 "sci.cfg"
#define SWITCH_CONFIG_FILE              "switch.cfg"
#define board_ntp_FILE                 "ntp-setting"
#define AUTH_PASSWD_FILE                "login.passwd"

/**
 * @brief 全局配置结构体
 */
typedef struct {
    char config_dir[256];               // 主配置目录
    char network_config_dir[256];       // 网络配置目录
    char backup_config_dir[256];        // 备份配置目录
    char log_dir[256];                  // 日志目录
    char auth_dir[256];                 // 认证文件目录
    int initialized;                    // 是否已初始化
} global_config_t;

/**
 * @brief 初始化全局配置
 * @param config_file 配置文件路径（可选）
 * @return 0成功，-1失败
 */
int global_config_init(const char *config_file);

/**
 * @brief 获取全局配置实例
 * @return 全局配置指针
 */
global_config_t *global_config_get(void);

/**
 * @brief 获取配置文件完整路径
 * @param config_name 配置文件名
 * @param full_path 输出完整路径的缓冲区
 * @param path_size 缓冲区大小
 * @return 0成功，-1失败
 */
int global_config_get_path(const char *config_name, char *full_path, size_t path_size);

/**
 * @brief 获取网络配置文件完整路径
 * @param config_name 配置文件名
 * @param full_path 输出完整路径的缓冲区
 * @param path_size 缓冲区大小
 * @return 0成功，-1失败
 */
int global_config_get_network_path(const char *config_name, char *full_path, size_t path_size);

/**
 * @brief 获取认证文件完整路径
 * @param config_name 认证文件名
 * @param full_path 输出完整路径的缓冲区
 * @param path_size 缓冲区大小
 * @return 0成功，-1失败
 */
int global_config_get_auth_path(const char *config_name, char *full_path, size_t path_size);

/**
 * @brief 设置配置目录
 * @param config_dir 配置目录路径
 * @return 0成功，-1失败
 */
int global_config_set_config_dir(const char *config_dir);

/**
 * @brief 设置网络配置目录
 * @param network_config_dir 网络配置目录路径
 * @return 0成功，-1失败
 */
int global_config_set_network_config_dir(const char *network_config_dir);

/**
 * @brief 设置认证文件目录
 * @param auth_dir 认证文件目录路径
 * @return 0成功，-1失败
 */
int global_config_set_auth_dir(const char *auth_dir);

/**
 * @brief 从配置文件加载目录设置
 * @param config_file 配置文件路径
 * @return 0成功，-1失败
 */
int global_config_load_from_file(const char *config_file);

/**
 * @brief 清理全局配置
 */
void global_config_cleanup(void);

#ifdef __cplusplus
}
#endif

#endif // GLOBAL_CONFIG_H 