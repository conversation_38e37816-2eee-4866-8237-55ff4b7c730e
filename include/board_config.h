/**
 * @file network_config.h
 * @brief 网络配置模块 - 兼容旧项目网络配置
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#ifndef NETWORK_CONFIG_H
#define NETWORK_CONFIG_H

#include <stdint.h>
#include <microhttpd.h>
#include <cjson/cJSON.h>

#ifdef __cplusplus
extern "C" {
#endif

// 兼容旧项目的配置文件路径定义
#define NETWORK_SETTING_CFG     "/etc/network-setting"     // 网络选择配置
#define ETHERNET_CFG            "/etc/eth0-setting"         // 以太网配置
#define WLAN_CFG                "/etc/wlan0-setting"        // 无线网络配置
#define WWAN_CFG                "/etc/3g-setting"           // 3G/4G网络配置

// 网络配置键值定义 - 兼容旧项目
#define NET_KEY_TYPE            "NET_TYPE"
#define NET_KEY_ETH1            "TEST_ETH1"
#define NET_KEY_ETH2            "TEST_ETH2"
#define NET_KEY_3G1             "TEST_3G1"
#define NET_KEY_3G2             "TEST_3G2"

#define ETH_KEY_IP              "IP"
#define ETH_KEY_MASK            "Mask"
#define ETH_KEY_GATEWAY         "Gateway"
#define ETH_KEY_DNS             "DNS"
#define ETH_KEY_MAC             "MAC"
#define ETH_KEY_WLAN            "WLAN"

// 网络类型枚举 - 兼容旧项目
typedef enum {
    NETWORK_TYPE_ETH_ONLY = 0,      // 仅以太网
    NETWORK_TYPE_WWAN_ONLY = 1,     // 仅无线广域网
    NETWORK_TYPE_ETH_WWAN = 2,      // 以太网优先，无线广域网备用
    NETWORK_TYPE_WWAN_ETH = 3       // 无线广域网优先，以太网备用
} network_type_e;

/**
 * @brief 网络选择配置结构 - 兼容旧项目stBoardNetCS
 */
typedef struct {
    int net_type;                   // 网络类型选择
    char test_eth1[16];            // 以太网测试地址1
    char test_eth2[16];            // 以太网测试地址2
    char test_3g1[16];             // 3G/4G测试地址1
    char test_3g2[16];             // 3G/4G测试地址2
} cfg_spec_netselection_t;

/**
 * @brief 以太网配置结构 - 兼容旧项目stNetConfig
 */
typedef struct {
    char ip[16];                   // IP地址字符串
    char mask[16];                 // 子网掩码
    char gateway[16];              // 网关地址
    char dns[16];                  // DNS服务器
    char mac[18];                  // MAC地址
    int wlan_enable;               // WLAN使能标志
} cfg_ethernet_t;

/**
 * @brief 二进制网络配置结构 - 兼容旧项目stCfgNet
 */
typedef struct {
    uint32_t ip;                   // IP地址（网络字节序）
    uint32_t mask;                 // 子网掩码（网络字节序）
    uint32_t gateway;              // 网关地址（网络字节序）
    uint32_t dns;                  // DNS服务器（网络字节序）
    uint8_t mac[6];                // MAC地址
} __attribute__((packed)) cfg_network_binary_t;

/**
 * @brief 初始化网络配置模块
 * @return 0成功，-1失败
 */
int network_config_init(void);

/**
 * @brief 清理网络配置模块
 */
void network_config_cleanup(void);

/**
 * @brief 获取网络选择配置 - GET /api/v1/config/spec/netcs
 * @param connection libmicrohttpd连接对象
 * @param url 请求URL
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小指针
 * @param con_cls 连接类指针
 * @return 0成功，-1失败
 */
int handle_spec_netselection_get(struct MHD_Connection *connection,
                                const char *url,
                                const char *method,
                                const char *upload_data,
                                size_t *upload_data_size,
                                void **con_cls);

/**
 * @brief 设置网络选择配置 - POST /api/v1/config/spec/netcs
 * @param connection libmicrohttpd连接对象
 * @param url 请求URL
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小指针
 * @param con_cls 连接类指针
 * @return 0成功，-1失败
 */
int handle_spec_netselection_post(struct MHD_Connection *connection,
                                 const char *url,
                                 const char *method,
                                 const char *upload_data,
                                 size_t *upload_data_size,
                                 void **con_cls);

/**
 * @brief 获取以太网配置 - GET /api/v1/config/board/ethernet
 * @param connection libmicrohttpd连接对象
 * @param url 请求URL
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小指针
 * @param con_cls 连接类指针
 * @return 0成功，-1失败
 */
int handle_board_ethernet_get(struct MHD_Connection *connection,
                               const char *url,
                               const char *method,
                               const char *upload_data,
                               size_t *upload_data_size,
                               void **con_cls);

/**
 * @brief 设置以太网配置 - POST /api/v1/config/board/ethernet
 * @param connection libmicrohttpd连接对象
 * @param url 请求URL
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小指针
 * @param con_cls 连接类指针
 * @return 0成功，-1失败
 */
int handle_board_ethernet_post(struct MHD_Connection *connection,
                                const char *url,
                                const char *method,
                                const char *upload_data,
                                size_t *upload_data_size,
                                void **con_cls);

/**
 * @brief 网络选择配置加载函数
 * @param config 配置结构指针
 * @return 0成功，-1失败
 */
int spec_netselection_load_config(void *config);

/**
 * @brief 网络选择配置保存函数
 * @param config 配置结构指针
 * @return 0成功，-1失败
 */
int spec_netselection_save_config(const void *config);

/**
 * @brief 网络选择配置验证函数
 * @param config 配置结构指针
 * @return 0成功，-1失败
 */
int spec_netselection_validate_config(const void *config);

/**
 * @brief 网络选择配置转JSON
 * @param config 配置结构指针
 * @param json JSON对象指针
 * @return 0成功，-1失败
 */
int spec_netselection_config_to_json(const void *config, cJSON **json);

/**
 * @brief JSON转网络选择配置
 * @param json JSON对象
 * @param config 配置结构指针
 * @return 0成功，-1失败
 */
int spec_netselection_json_to_config(const cJSON *json, void *config);

/**
 * @brief 以太网配置加载函数
 * @param config 配置结构指针
 * @return 0成功，-1失败
 */
int network_ethernet_load_config(void *config);

/**
 * @brief 以太网配置保存函数
 * @param config 配置结构指针
 * @return 0成功，-1失败
 */
int network_ethernet_save_config(const void *config);

/**
 * @brief 以太网配置验证函数
 * @param config 配置结构指针
 * @return 0成功，-1失败
 */
int network_ethernet_validate_config(const void *config);

/**
 * @brief 以太网配置转JSON
 * @param config 配置结构指针
 * @param json JSON对象指针
 * @return 0成功，-1失败
 */
int network_ethernet_config_to_json(const void *config, cJSON **json);

/**
 * @brief JSON转以太网配置
 * @param json JSON对象
 * @param config 配置结构指针
 * @return 0成功，-1失败
 */
int network_ethernet_json_to_config(const cJSON *json, void *config);

/**
 * @brief 设置网络选择配置默认值
 * @param config 配置结构指针
 */
void spec_netselection_set_default(cfg_spec_netselection_t *config);

/**
 * @brief 设置以太网配置默认值
 * @param config 配置结构指针
 */
void network_ethernet_set_default(cfg_ethernet_t *config);

#ifdef __cplusplus
}
#endif

#endif /* NETWORK_CONFIG_H */
