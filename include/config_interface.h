/**
 * @file config_interface.h
 * @brief 配置管理接口模块
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#ifndef CONFIG_INTERFACE_H
#define CONFIG_INTERFACE_H

#include <stddef.h>
#include <cjson/cJSON.h>

#ifdef __cplusplus
extern "C" {
#endif

// 最大配置模块数量
#define MAX_CONFIG_MODULES 32

/**
 * @brief 配置模块结构体
 */
typedef struct {
    char *module_name;                    // 模块名称
    char *config_file_path;              // 配置文件路径
    size_t config_struct_size;           // 配置结构大小
    int (*load_config)(void *config);    // 加载配置
    int (*save_config)(const void *config); // 保存配置
    int (*validate_config)(const void *config); // 验证配置
    int (*config_to_json)(const void *config, cJSON **json); // 配置转JSON
    int (*json_to_config)(const cJSON *json, void *config);  // JSON转配置
} config_module_t;

/**
 * @brief 配置操作结果枚举
 */
typedef enum {
    CONFIG_SUCCESS = 0,        // 成功
    CONFIG_ERROR_INVALID_PARAM, // 无效参数
    CONFIG_ERROR_FILE_NOT_FOUND, // 文件未找到
    CONFIG_ERROR_FILE_READ,     // 文件读取错误
    CONFIG_ERROR_FILE_WRITE,    // 文件写入错误
    CONFIG_ERROR_VALIDATION,    // 验证失败
    CONFIG_ERROR_JSON_PARSE,    // JSON解析错误
    CONFIG_ERROR_MEMORY,        // 内存错误
    CONFIG_ERROR_UNKNOWN        // 未知错误
} config_result_e;

/**
 * @brief 初始化配置管理系统
 * @return 0成功，-1失败
 */
int config_manager_init(void);

/**
 * @brief 初始化配置管理系统
 * @return 0成功，-1失败
 */
int config_manager_init(void);

/**
 * @brief 清理配置管理系统
 */
void config_manager_cleanup(void);

/**
 * @brief 获取已注册模块数量
 * @return 模块数量
 */
int config_manager_get_module_count(void);

/**
 * @brief 注册配置模块
 * @param module 配置模块指针
 * @return 0成功，-1失败
 */
int config_manager_register(config_module_t *module);

/**
 * @brief 根据模块名查找配置模块
 * @param module_name 模块名称
 * @return 配置模块指针，NULL表示未找到
 */
config_module_t *config_manager_find(const char *module_name);

/**
 * @brief 加载指定模块的配置
 * @param module_name 模块名称
 * @param config 配置结构指针
 * @return config_result_e 结果码
 */
config_result_e config_manager_load(const char *module_name, void *config);

/**
 * @brief 保存指定模块的配置
 * @param module_name 模块名称
 * @param config 配置结构指针
 * @return config_result_e 结果码
 */
config_result_e config_manager_save(const char *module_name, const void *config);

/**
 * @brief 验证指定模块的配置
 * @param module_name 模块名称
 * @param config 配置结构指针
 * @return config_result_e 结果码
 */
config_result_e config_manager_validate(const char *module_name, const void *config);

/**
 * @brief 配置转JSON
 * @param module_name 模块名称
 * @param config 配置结构指针
 * @param json JSON对象指针
 * @return config_result_e 结果码
 */
config_result_e config_manager_to_json(const char *module_name, const void *config, cJSON **json);

/**
 * @brief JSON转配置
 * @param module_name 模块名称
 * @param json JSON对象
 * @param config 配置结构指针
 * @return config_result_e 结果码
 */
config_result_e config_manager_from_json(const char *module_name, const cJSON *json, void *config);

/**
 * @brief 备份指定模块的配置文件
 * @param module_name 模块名称
 * @return config_result_e 结果码
 */
config_result_e config_manager_backup(const char *module_name);

/**
 * @brief 恢复指定模块的配置文件
 * @param module_name 模块名称
 * @return config_result_e 结果码
 */
config_result_e config_manager_restore(const char *module_name);

/**
 * @brief 获取错误描述
 * @param result 结果码
 * @return 错误描述字符串
 */
const char *config_manager_error_string(config_result_e result);

#ifdef __cplusplus
}
#endif

#endif /* CONFIG_INTERFACE_H */
