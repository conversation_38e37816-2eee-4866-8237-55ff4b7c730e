/**
 * @file recorder_config.h
 * @brief 录音模块配置管理接口 - 对应旧项目0recorder.c（简化版）
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#ifndef RECORDER_CONFIG_H
#define RECORDER_CONFIG_H

#include <stdint.h>
#include <cjson/cJSON.h>
#include <microhttpd.h>

// 录音模块默认配置值 - 对应旧项目第一个基站默认值
#define RECORDER_DEFAULT_SERVER_IP      "*************"
#define RECORDER_DEFAULT_SERVER_PORT    8000
#define RECORDER_DEFAULT_LOCAL_PORT     8001
#define RECORDER_DEFAULT_TIMEOUT        30
#define RECORDER_DEFAULT_DEVICE_TYPE    0x13  // 录音模块

/**
 * @brief 录音配置结构体 - 严格对应旧项目stCfgPeerBase结构
 * 基于旧项目0recorder.c和0mini.c的真实实现，使用相同的数据结构和字段含义
 */
typedef struct {
    uint8_t     peer_sum;                   // 基站数量（新项目固定为1）
    struct {
        uint32_t    peer_ip;                // 基站IP地址
        uint16_t    peer_port;              // 基站端口
        uint8_t     device_type;            // 设备类型：0x13-录音模块，0x17-最小基站
        uint16_t    voice_port;             // 基站语音端口
        uint16_t    data_port;              // 基站数据端口
        uint16_t    timeout;                // 基站超时时间（秒）
        uint8_t     reserved[2];            // 保留字段
    } peer[8];                              // 基站配置数组（最大8个）
} __attribute__((packed)) cfg_recorder_t;

/**
 * @brief 设置录音配置默认值
 * @param config 录音配置结构指针
 */
void recorder_config_set_default(cfg_recorder_t *config);

/**
 * @brief 加载录音配置
 * @param config 录音配置结构指针
 * @return 0成功，-1失败
 */
int recorder_config_load(cfg_recorder_t *config);

/**
 * @brief 保存录音配置
 * @param config 录音配置结构指针
 * @return 0成功，-1失败
 */
int recorder_config_save(const cfg_recorder_t *config);

/**
 * @brief 验证录音配置
 * @param config 录音配置结构指针
 * @return 0有效，-1无效
 */
int recorder_config_validate(const cfg_recorder_t *config);

/**
 * @brief 录音配置转JSON
 * @param config 录音配置结构指针
 * @param json JSON对象指针的指针
 * @return 0成功，-1失败
 */
int recorder_config_to_json(const cfg_recorder_t *config, cJSON **json);

/**
 * @brief JSON转录音配置
 * @param json JSON对象指针
 * @param config 录音配置结构指针
 * @return 0成功，-1失败
 */
int recorder_json_to_config(const cJSON *json, cfg_recorder_t *config);

// 配置管理器接口函数（按命名规范）
int recorder_module_load(void *config);
int recorder_module_save(const void *config);
int recorder_module_validate(const void *config);
int recorder_module_to_json(const void *config, cJSON **json);
int recorder_module_from_json(const cJSON *json, void *config);

// API处理函数（按命名规范）
/**
 * @brief 获取录音配置API处理函数
 * @param connection HTTP连接
 * @param url URL路径
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小指针
 * @param con_cls 连接类指针
 * @return 0成功，-1失败
 */
int handle_recorder_get(struct MHD_Connection *connection,
                       const char *url,
                       const char *method,
                       const char *upload_data,
                       size_t *upload_data_size,
                       void **con_cls);

/**
 * @brief 设置录音配置API处理函数
 * @param connection HTTP连接
 * @param url URL路径
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小指针
 * @param con_cls 连接类指针
 * @return 0成功，-1失败
 */
int handle_recorder_post(struct MHD_Connection *connection,
                        const char *url,
                        const char *method,
                        const char *upload_data,
                        size_t *upload_data_size,
                        void **con_cls);

/**
 * @brief 初始化录音配置模块
 * @return 0成功，-1失败
 */
int recorder_config_init(void);

/**
 * @brief 清理录音配置模块
 */
void recorder_config_cleanup(void);

#endif // RECORDER_CONFIG_H
