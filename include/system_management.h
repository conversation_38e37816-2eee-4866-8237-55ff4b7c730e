/**
 * @file system_management.h
 * @brief 系统管理功能接口 - 对应旧项目0system.c
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#ifndef system_management_H
#define system_management_H

#include <stdint.h>
#include <cjson/cJSON.h>
#include "api_router.h"

// 系统管理操作类型（对应旧项目0system.c功能）
typedef enum {
    SYSTEM_OP_REBOOT = 0,       // 系统重启
    SYSTEM_OP_RESET = 1,        // 配置重置
    SYSTEM_OP_UPLOAD = 2        // 文件上传
} system_operation_t;

// 日志类型枚举（对应旧项目0down.c功能）
typedef enum {
    LOG_TYPE_STARTUP = 0,       // 启动初始化日志
    LOG_TYPE_3G_INFO = 1,       // 3G网络信息
    LOG_TYPE_3G_DIAL = 2,       // 3G拨号日志
    LOG_TYPE_WLAN = 3           // 无线网络日志
} log_type_t;

/**
 * @brief 执行系统重启
 * @return 0成功，-1失败
 */
int system_do_reboot(void);

/**
 * @brief 执行配置重置
 * @return 0成功，-1失败
 */
int system_do_reset(void);

/**
 * @brief 获取系统日志（对应旧项目0down.c）
 * @param log_buffer 日志缓冲区
 * @param buffer_size 缓冲区大小
 * @return 0成功，-1失败
 */
int system_get_logs(char *log_buffer, size_t buffer_size);

/**
 * @brief 获取3G信号强度（对应旧项目0down.c）
 * @param rssi 信号强度指针（0-31）
 * @param ber 误码率指针
 * @param dbm 信号功率指针
 * @return 0成功，-1失败，-2超时，-3检测失败
 */
int system_get_signal_strength(char *rssi, char *ber, char *dbm);

/**
 * @brief 获取系统日志内容（对应旧项目0down.c）
 * @param log_type 日志类型
 * @param log_buffer 日志缓冲区
 * @param buffer_size 缓冲区大小
 * @return 0成功，-1失败
 */
int system_get_log_content(log_type_t log_type, char *log_buffer, size_t buffer_size);

/**
 * @brief 文件上传处理（对应旧项目0system.c）
 * @param filename 文件名
 * @param data 文件数据
 * @param size 文件大小
 * @return 0成功，-1失败
 */
int system_do_upload(const char *filename, const char *data, size_t size);

// API处理函数（按命名规范）
/**
 * @brief 系统重启API处理函数
 * @param connection HTTP连接
 * @param request_data 请求数据
 * @return API响应结构
 */
int handle_system_reboot(struct MHD_Connection *connection,
                         const char *url,
                         const char *method,
                         const char *upload_data,
                         size_t *upload_data_size,
                         void **con_cls);

/**
 * @brief 系统重置API处理函数
 * @param connection HTTP连接
 * @param url 请求URL
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小
 * @param con_cls 连接状态
 * @return 0成功，-1失败
 */
int handle_system_reset(struct MHD_Connection *connection,
                       const char *url,
                       const char *method,
                       const char *upload_data,
                       size_t *upload_data_size,
                       void **con_cls);

/**
 * @brief 系统日志API处理函数（对应旧项目0down.c）
 * @param connection HTTP连接
 * @param url 请求URL
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小
 * @param con_cls 连接状态
 * @return 0成功，-1失败
 */
int handle_system_logs(struct MHD_Connection *connection,
                      const char *url,
                      const char *method,
                      const char *upload_data,
                      size_t *upload_data_size,
                      void **con_cls);

/**
 * @brief 3G信号强度API处理函数（对应旧项目0down.c）
 * @param connection HTTP连接
 * @param url 请求URL
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小
 * @param con_cls 连接状态
 * @return 0成功，-1失败
 */
int handle_system_signal(struct MHD_Connection *connection,
                        const char *url,
                        const char *method,
                        const char *upload_data,
                        size_t *upload_data_size,
                        void **con_cls);

/**
 * @brief 文件上传API处理函数（对应旧项目0system.c）
 * @param connection HTTP连接
 * @param url 请求URL
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小
 * @param con_cls 连接状态
 * @return 0成功，-1失败
 */
int handle_system_upload(struct MHD_Connection *connection,
                        const char *url,
                        const char *method,
                        const char *upload_data,
                        size_t *upload_data_size,
                        void **con_cls);

/**
 * @brief 初始化系统管理模块
 * @return 0成功，-1失败
 */
int system_management_init(void);

/**
 * @brief 清理系统管理模块
 */
void system_management_cleanup(void);

// 系统管理相关路径定义
#define SYSTEM_CONFIG_DIR       "/home/<USER>/cfg"
#define SYSTEM_BACKUP_DIR       "/home/<USER>/backup"
#define SYSTEM_DEFAULT_CONFIG_DIR "/home/<USER>/cfg/default"

// 系统版本信息
#define SYSTEM_VERSION          "1.0.0"
#define SYSTEM_BUILD_TIME       __DATE__ " " __TIME__

#endif // system_management_H
