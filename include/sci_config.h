/**
 * @file sci_config.h
 * @brief SCI基站配置管理接口 - 对应旧项目0sci.c
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#ifndef SCI_CONFIG_H
#define SCI_CONFIG_H

#include <stdint.h>
#include <cjson/cJSON.h>
#include <microhttpd.h>

// SCI模块默认配置值（对应旧项目）
#define SCI_DEFAULT_VOICE_IP        "*************"
#define SCI_DEFAULT_DATA_PORT       2600
#define SCI_DEFAULT_VOICE_PORT      2800
#define SCI_DEFAULT_NET_DS          10
#define SCI_DEFAULT_NET_SW          1
#define SCI_DEFAULT_NET_BS          1
#define SCI_DEFAULT_BASE_TYPE       1    // 单工基站
#define SCI_DEFAULT_VCHAN_NUM       16
#define SCI_DEFAULT_BUFFER_TIME     100  // 毫秒
#define SCI_DEFAULT_DOWN_TIME       30   // 秒
#define SCI_DEFAULT_RESET_TIME      10   // 秒

/**
 * @brief SCI基站配置结构体 - 严格对应旧项目stCfgSci
 * 完全对应旧项目1rwconference.h中的stCfgSci_结构体
 */
typedef struct {
    uint8_t     get_cfg_method;     // 配置获取方式（0-自动，1-手动）
    uint8_t     network_mode;       // 网络模式（0-点对点，1-中继）
    uint32_t    voice_ip;          // 语音IP地址
    uint16_t    data_listen_port;   // 数据监听端口
    uint16_t    vbus_base_port;     // 语音总线基础端口
    uint32_t    net_address;        // 网络地址（24位，使用低24位）
    uint8_t     base_type;          // 基站类型（1-单工，2-双工）
    uint8_t     vbus_to_chan[12];   // 语音总线到通道映射
    uint8_t     vcan_number;        // 语音通道数量
    uint16_t    buffertime;         // 缓冲时间（毫秒）
    uint16_t    downtime;           // 掉线时间（秒）
    uint8_t     resettime;          // 重置时间（秒）
    uint8_t     reserved[2];        // 保留字段
} cfg_sci_t;

/**
 * @brief 设置SCI配置默认值
 * @param config SCI配置结构指针
 */
void sci_config_set_default(cfg_sci_t *config);

/**
 * @brief 加载SCI配置
 * @param config SCI配置结构指针
 * @return 0成功，-1失败
 */
int sci_config_load(cfg_sci_t *config);

/**
 * @brief 保存SCI配置
 * @param config SCI配置结构指针
 * @return 0成功，-1失败
 */
int sci_config_save(const cfg_sci_t *config);

/**
 * @brief 验证SCI配置
 * @param config SCI配置结构指针
 * @return 0有效，-1无效
 */
int sci_config_validate(const cfg_sci_t *config);

/**
 * @brief SCI配置转JSON
 * @param config SCI配置结构指针
 * @param json JSON对象指针的指针
 * @return 0成功，-1失败
 */
int sci_config_to_json(const cfg_sci_t *config, cJSON **json);

/**
 * @brief JSON转SCI配置
 * @param json JSON对象指针
 * @param config SCI配置结构指针
 * @return 0成功，-1失败
 */
int sci_json_to_config(const cJSON *json, cfg_sci_t *config);

// 配置管理器接口函数（按命名规范）
int sci_module_load(void *config);
int sci_module_save(const void *config);
int sci_module_validate(const void *config);
int sci_module_to_json(const void *config, cJSON **json);
int sci_module_from_json(const cJSON *json, void *config);

// API处理函数（按命名规范）
/**
 * @brief 获取SCI配置API处理函数
 * @param connection HTTP连接
 * @param url URL路径
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小指针
 * @param con_cls 连接类指针
 * @return 0成功，-1失败
 */
int handle_sci_get(struct MHD_Connection *connection,
                  const char *url,
                  const char *method,
                  const char *upload_data,
                  size_t *upload_data_size,
                  void **con_cls);

/**
 * @brief 设置SCI配置API处理函数
 * @param connection HTTP连接
 * @param url URL路径
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小指针
 * @param con_cls 连接类指针
 * @return 0成功，-1失败
 */
int handle_sci_post(struct MHD_Connection *connection,
                   const char *url,
                   const char *method,
                   const char *upload_data,
                   size_t *upload_data_size,
                   void **con_cls);

/**
 * @brief 初始化SCI配置模块
 * @return 0成功，-1失败
 */
int sci_config_init(void);

/**
 * @brief 清理SCI配置模块
 */
void sci_config_cleanup(void);

#endif // SCI_CONFIG_H
