/**
 * @file center_config.h
 * @brief 呼叫中心配置管理模块
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#ifndef CENTER_CONFIG_H
#define CENTER_CONFIG_H

#include <stdint.h>
#include <cjson/cJSON.h>
#include <microhttpd.h>

/**
 * @brief 呼叫中心配置结构体 - 严格对应旧项目stCfgCenter
 * 完全按照deprecated/cgi/inc/1rwcenter.h中的stCfgCenter_结构实现
 */
typedef struct {
    uint32_t center_no;         // 呼叫中心编号（24位，使用MAKEADDR宏构建）
    uint32_t center_outssi;     // 呼叫中心统一编号（24位）
    uint32_t center_inssi;      // 呼叫中心统一应答号（24位）
    uint8_t  vchan_sum;         // 语音通道数量
    uint16_t center_voice_port; // 呼叫中心语音端口
    uint16_t listen_agent_port; // 监听代理端口
    uint8_t  peer_net_type;     // 对等网络类型（0-单播，1-广播，2-组播）
    uint32_t send_all_agent_ip; // 发送所有代理IP
    uint16_t send_to_agent_port;// 发送到代理端口
    uint16_t inssi_num;         // 应答号个数
    uint16_t spec_function;     // 特殊功能标志
    uint8_t  reserved[1];       // 保留字段，保持结构体对齐
} __attribute__((packed)) cfg_center_t;

/**
 * @brief 初始化呼叫中心配置模块
 * @return 0成功，-1失败
 */
int center_config_init(void);

/**
 * @brief 清理呼叫中心配置模块
 */
void center_config_cleanup(void);

/**
 * @brief 获取呼叫中心配置信息（API处理函数）
 * @param connection HTTP连接
 * @param url 请求URL
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小
 * @param con_cls 连接状态
 * @return 0成功，-1失败
 */
int handle_center_get(struct MHD_Connection *connection, 
                     const char *url, 
                     const char *method,
                     const char *upload_data, 
                     size_t *upload_data_size,
                     void **con_cls);

/**
 * @brief 设置呼叫中心配置信息（API处理函数）
 * @param connection HTTP连接
 * @param url 请求URL
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小
 * @param con_cls 连接状态
 * @return 0成功，-1失败
 */
int handle_center_post(struct MHD_Connection *connection, 
                      const char *url, 
                      const char *method,
                      const char *upload_data, 
                      size_t *upload_data_size,
                      void **con_cls);

/**
 * @brief 加载呼叫中心配置
 * @param config 配置结构指针
 * @return 0成功，-1失败
 */
int center_config_load(cfg_center_t *config);

/**
 * @brief 保存呼叫中心配置
 * @param config 配置结构指针
 * @return 0成功，-1失败
 */
int center_config_save(const cfg_center_t *config);

/**
 * @brief 验证呼叫中心配置
 * @param config 配置结构指针
 * @return 0有效，-1无效
 */
int center_config_validate(const cfg_center_t *config);

/**
 * @brief 配置结构转JSON
 * @param config 配置结构指针
 * @param json JSON对象指针
 * @return 0成功，-1失败
 */
int center_config_to_json(const cfg_center_t *config, cJSON **json);

/**
 * @brief JSON转配置结构
 * @param json JSON对象
 * @param config 配置结构指针
 * @return 0成功，-1失败
 */
int center_json_to_config(const cJSON *json, cfg_center_t *config);

/**
 * @brief 设置默认配置
 * @param config 配置结构指针
 */
void center_config_set_default(cfg_center_t *config);

// 默认配置值 - 对应旧项目常量定义
#define CENTER_DEFAULT_VOICE_PORT           2800    // FUNCTION_VOICE_PEER_PORT_BASE
#define CENTER_DEFAULT_AGENT_PORT           2702    // CALLCENTER_RECV_AGENT_PORT
#define CENTER_DEFAULT_AGENT_SEND_PORT      2704    // CALLCENTER_AGENT_RECV_PORT
#define CENTER_DEFAULT_VCHAN_SUM            16      // MAX_VOCODER_CHAN
#define CENTER_DEFAULT_DS                   10      // 默认调度系统号
#define CENTER_DEFAULT_SW                   1       // 默认交换机号
#define CENTER_DEFAULT_BS                   28      // 默认基站号
#define CENTER_DEFAULT_OUTSSI               0xFC02B1  // 默认呼出号码
#define CENTER_DEFAULT_INSSI                0xFC02B1  // 默认呼入号码
#define CENTER_DEFAULT_INSSI_NUM            10      // 默认应答号个数
#define CENTER_DEFAULT_AGENT_IP             "*********"  // 默认代理IP

// 地址编码宏 - 对应旧项目MAKEADDR
#define MAKEADDR(ds, sw, bs, type) (((ds) << 16) | ((sw) << 8) | (bs))
#define GETDS(addr) (((addr) >> 16) & 0x3F)
#define GETSW(addr) (((addr) >> 8) & 0xFF)
#define GETBS(addr) ((addr) & 0x1F)

#endif // CENTER_CONFIG_H
