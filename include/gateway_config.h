/**
 * @file gateway_config.h
 * @brief 网关配置管理模块 - 对应旧项目0gateway.c
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#ifndef GATEWAY_CONFIG_H
#define GATEWAY_CONFIG_H

#include <stdint.h>
#include <cjson/cJSON.h>
#include <microhttpd.h>
#include <arpa/inet.h>

// 使用center_config.h中的地址编码宏
#include "center_config.h"


// 网关配置结构体 - 严格对应旧项目stCfgCenter
typedef struct {
    uint32_t center_no;         // 网关编号（24位，使用MAKEADDR宏构建）
    uint32_t center_outssi;     // 网关外呼号码（24位）
    uint32_t center_inssi;      // 网关内呼号码（24位）
    uint8_t  vchan_sum;         // 语音通道总数
    uint16_t center_voice_port; // 网关语音端口
    uint16_t listen_agent_port; // 监听代理端口
    uint8_t  peer_net_type;     // 对等网络类型（0=单播，1=广播，2=组播）
    uint32_t send_all_agent_ip; // 广播/组播IP地址
    uint16_t send_to_agent_port;// 发送到代理端口
    uint16_t inssi_num;         // 呼入号码数量
    uint16_t spec_function;     // 特殊功能标志
    uint8_t  reserved[1];       // 保留字段，保持结构体对齐
} __attribute__((packed)) cfg_gateway_t;

/**
 * @brief 初始化网关配置模块
 * @return 0成功，-1失败
 */
int gateway_config_init(void);

/**
 * @brief 清理网关配置模块
 */
void gateway_config_cleanup(void);

/**
 * @brief 获取网关配置信息（API处理函数）
 * @param connection HTTP连接
 * @param url 请求URL
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小
 * @param con_cls 连接状态
 * @return 0成功，-1失败
 */
int handle_gateway_get(struct MHD_Connection *connection, 
                      const char *url, 
                      const char *method,
                      const char *upload_data, 
                      size_t *upload_data_size,
                      void **con_cls);

/**
 * @brief 设置网关配置信息（API处理函数）
 * @param connection HTTP连接
 * @param url 请求URL
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小
 * @param con_cls 连接状态
 * @return 0成功，-1失败
 */
int handle_gateway_post(struct MHD_Connection *connection, 
                       const char *url, 
                       const char *method,
                       const char *upload_data, 
                       size_t *upload_data_size,
                       void **con_cls);

/**
 * @brief 加载网关配置
 * @param config 配置结构指针
 * @return 0成功，-1失败
 */
int gateway_config_load(cfg_gateway_t *config);

/**
 * @brief 保存网关配置
 * @param config 配置结构指针
 * @return 0成功，-1失败
 */
int gateway_config_save(const cfg_gateway_t *config);

/**
 * @brief 验证网关配置
 * @param config 配置结构指针
 * @return 0有效，-1无效
 */
int gateway_config_validate(const cfg_gateway_t *config);

/**
 * @brief 配置结构转JSON
 * @param config 配置结构指针
 * @param json JSON对象指针
 * @return 0成功，-1失败
 */
int gateway_config_to_json(const cfg_gateway_t *config, cJSON **json);

/**
 * @brief JSON转配置结构
 * @param json JSON对象
 * @param config 配置结构指针
 * @return 0成功，-1失败
 */
int gateway_json_to_config(const cJSON *json, cfg_gateway_t *config);

/**
 * @brief 设置默认配置
 * @param config 配置结构指针
 */
void gateway_config_set_default(cfg_gateway_t *config);

// 默认配置值（对应旧项目默认值）
#define GATEWAY_DEFAULT_DS          10      // 调度系统号
#define GATEWAY_DEFAULT_SW          1       // 交换机号
#define GATEWAY_DEFAULT_BS          29      // 基站号
#define GATEWAY_DEFAULT_OUTSSI      0xFC02B1  // 外呼号码
#define GATEWAY_DEFAULT_INSSI       0xFC02B1  // 内呼号码
#define GATEWAY_DEFAULT_VCHAN_SUM   16      // 语音通道数
#define GATEWAY_DEFAULT_VOICE_PORT  2800    // 语音端口
#define GATEWAY_DEFAULT_AGENT_PORT  2702    // 监听代理端口
#define GATEWAY_DEFAULT_AGENT_IP    "*********"  // 代理IP
#define GATEWAY_DEFAULT_AGENT_SEND_PORT 2704  // 代理发送端口
#define GATEWAY_DEFAULT_INSSI_NUM   10      // 呼入号码个数

#endif // GATEWAY_CONFIG_H
