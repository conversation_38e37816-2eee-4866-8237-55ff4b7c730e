/**
 * @file password_reader.h
 * @brief 密码文件读取器接口
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#ifndef PASSWORD_READER_H
#define PASSWORD_READER_H

#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 从密码文件读取用户密码
 * @param username 用户名
 * @param password_hash 输出的密码哈希
 * @param hash_size 哈希缓冲区大小
 * @return 0成功，-1失败
 */
int password_reader_get_user_hash(const char *username, char *password_hash, size_t hash_size);

/**
 * @brief 验证用户密码
 * @param username 用户名
 * @param password 明文密码
 * @return 0验证成功，-1验证失败
 */
int password_reader_verify_user(const char *username, const char *password);

/**
 * @brief 检查密码文件是否存在
 * @return 1存在，0不存在
 */
int password_reader_file_exists(void);

#ifdef __cplusplus
}
#endif

#endif // PASSWORD_READER_H 